"""
HIL (Human-in-the-Loop) wrapper tools untuk hotel operations.
Tools ini menambahkan approval step sebelum operasi penting.
"""

from typing import Optional, Dict, Any
from langchain_core.tools import tool
from langgraph.types import interrupt
import logging

# Import original tools
from tools.tools import (
    search_hotels_by_location as _search_hotels_by_location,
    book_hotel_room as _book_hotel_room,
    process_hotel_payment as _process_hotel_payment
)

@tool
def search_hotels_by_location_hil(location: str):
    """
    Mencari hotel berdasarkan lokasi dengan HIL approval.

    Args:
        location (str): Lokasi hotel yang ingin dicari

    Returns:
        String: Hasil pencarian hotel atau permintaan approval
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"🔥 HIL Tool called: search_hotels_by_location_hil with location={location}")

    # Trigger HIL approval - menggunakan pola yang sama seperti referensi
    logger.info("🔥 Triggering HIL interrupt...")
    approval_response = interrupt({
        "tool": "search_hotels_by_location",
        "args": {"location": location},
        "message": f"🏨 Konfirmasi Pencarian Hotel\n\nSistem akan mencari hotel di {location} menggunakan database internal.\n\nApakah Anda ingin melanjutkan pencarian?\nKlik tombol di bawah untuk konfirmasi:"
    })

    logger.info(f"🔥 HIL approval response: {approval_response}")

    # Handle approval response
    if isinstance(approval_response, dict):
        response_type = approval_response.get("type", "reject")
    elif isinstance(approval_response, str):
        response_type = "accept" if approval_response.lower() in ["yes", "ok", "y"] else "reject"
    else:
        response_type = "reject"

    if response_type == "reject":
        return f"❌ Pencarian hotel di {location} dibatalkan oleh user."

    # Lanjutkan dengan pencarian asli
    logger.info("🔥 Proceeding with original search...")
    return _search_hotels_by_location(location)

@tool
def book_hotel_room_hil(hotel_id: int, check_in_date: str, check_out_date: str,
                        jumlah_tamu: int, jumlah_kamar: int, tipe_kamar: str,
                        nama_pemesan: str = "", email: str = "", telepon: str = "",
                        user_id: Optional[int] = None, catatan: Optional[str] = None):
    """
    Membuat pemesanan kamar hotel dengan HIL approval.

    Args:
        hotel_id (int): ID hotel
        check_in_date (str): Tanggal check-in dalam format YYYY-MM-DD
        check_out_date (str): Tanggal check-out dalam format YYYY-MM-DD
        jumlah_tamu (int): Jumlah tamu yang menginap
        jumlah_kamar (int): Jumlah kamar yang dipesan
        tipe_kamar (str): Tipe kamar yang dipesan
        nama_pemesan (str, optional): Nama lengkap pemesan
        email (str, optional): Alamat email pemesan
        telepon (str, optional): Nomor telepon pemesan
        user_id (int, optional): ID pengguna jika sudah terdaftar
        catatan (str, optional): Catatan tambahan untuk pemesanan

    Returns:
        String: Hasil pemesanan atau permintaan approval
    """
    # Trigger HIL approval
    booking_details = f"""Konfirmasi pemesanan hotel:
- Hotel ID: {hotel_id}
- Check-in: {check_in_date}
- Check-out: {check_out_date}
- Jumlah Tamu: {jumlah_tamu}
- Jumlah Kamar: {jumlah_kamar}
- Tipe Kamar: {tipe_kamar}
- Nama Pemesan: {nama_pemesan}
- Email: {email}
- Telepon: {telepon}
- Catatan: {catatan or 'Tidak ada'}

Apakah Anda ingin melanjutkan pemesanan?
Ketik 'yes' untuk melanjutkan, 'no' untuk membatalkan"""

    approval_response = interrupt({
        "tool": "book_hotel_room",
        "args": {
            "hotel_id": hotel_id,
            "check_in_date": check_in_date,
            "check_out_date": check_out_date,
            "jumlah_tamu": jumlah_tamu,
            "jumlah_kamar": jumlah_kamar,
            "tipe_kamar": tipe_kamar,
            "nama_pemesan": nama_pemesan,
            "email": email,
            "telepon": telepon,
            "catatan": catatan
        },
        "message": booking_details
    })

    # Handle approval response
    if isinstance(approval_response, dict):
        response_type = approval_response.get("type", "reject")
    elif isinstance(approval_response, str):
        response_type = "accept" if approval_response.lower() in ["yes", "ok", "y"] else "reject"
    else:
        response_type = "reject"

    if response_type == "reject":
        return f"Pemesanan hotel dibatalkan oleh user."

    # Lanjutkan dengan pemesanan asli
    return _book_hotel_room(
        hotel_id, check_in_date, check_out_date, jumlah_tamu, jumlah_kamar,
        tipe_kamar, nama_pemesan, email, telepon, user_id, catatan
    )

@tool
def process_hotel_payment_hil(booking_id: int, metode_pembayaran: str):
    """
    Memproses pembayaran untuk pemesanan hotel dengan HIL approval.

    Args:
        booking_id: ID pemesanan hotel
        metode_pembayaran: Metode pembayaran ('transfer bank', 'kartu kredit', 'e-wallet')

    Returns:
        String berisi konfirmasi pembayaran atau permintaan approval
    """
    # Trigger HIL approval
    payment_details = f"""Konfirmasi pembayaran hotel:
- Booking ID: {booking_id}
- Metode Pembayaran: {metode_pembayaran}

Apakah Anda ingin melanjutkan pembayaran?
Ketik 'yes' untuk melanjutkan, 'no' untuk membatalkan"""

    approval_response = interrupt({
        "tool": "process_hotel_payment",
        "args": {
            "booking_id": booking_id,
            "metode_pembayaran": metode_pembayaran
        },
        "message": payment_details
    })

    # Handle approval response
    if isinstance(approval_response, dict):
        response_type = approval_response.get("type", "reject")
    elif isinstance(approval_response, str):
        response_type = "accept" if approval_response.lower() in ["yes", "ok", "y"] else "reject"
    else:
        response_type = "reject"

    if response_type == "reject":
        return f"Pembayaran hotel dibatalkan oleh user."

    # Lanjutkan dengan pembayaran asli
    return _process_hotel_payment(booking_id, metode_pembayaran)

# List HIL tools untuk export
HIL_HOTEL_TOOLS = [
    search_hotels_by_location_hil,
    book_hotel_room_hil,
    process_hotel_payment_hil
]
