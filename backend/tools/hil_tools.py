"""
HIL (Human-in-the-Loop) wrapper tools untuk hotel operations.
Tools ini menambahkan approval step sebelum operasi penting.
"""

from typing import Optional, Dict, Any
from langchain_core.tools import tool
from langgraph.types import interrupt
import logging

# Import original tools
from tools.tools import (
    search_hotels_by_location as _search_hotels_by_location,
    book_hotel_room as _book_hotel_room,
    process_hotel_payment as _process_hotel_payment
)

@tool
async def search_hotels_by_location_hil(location: str, require_approval: bool = True):
    """
    Mencari hotel berdasarkan lokasi dengan HIL approval.
    
    Args:
        location (str): Lokasi hotel yang ingin dicari
        require_approval (bool): Apakah memerlukan approval user
    
    Returns:
        String: Hasil pencarian hotel atau permintaan approval
    """
    if require_approval:
        # Trigger HIL approval
        approval_data = {
            "type": "hotel_search",
            "location": location,
            "message": f"Apakah Anda ingin mencari hotel di {location}? Sistem akan menggunakan booking.com, Airbnb, dan TripAdvisor untuk pencarian."
        }
        
        # Interrupt untuk approval
        user_approval = interrupt(f"Hotel Search Approval: {approval_data}")
        
        # Check approval
        if isinstance(user_approval, dict):
            approved = user_approval.get("approved", False)
        else:
            approved = bool(user_approval)
            
        if not approved:
            return f"Pencarian hotel di {location} dibatalkan oleh user."
    
    # Lanjutkan dengan pencarian asli
    return await _search_hotels_by_location(location)

@tool
async def book_hotel_room_hil(hotel_id: int, check_in_date: str, check_out_date: str,
                              jumlah_tamu: int, jumlah_kamar: int, tipe_kamar: str,
                              nama_pemesan: str = "", email: str = "", telepon: str = "",
                              user_id: Optional[int] = None, catatan: Optional[str] = None,
                              require_approval: bool = True):
    """
    Membuat pemesanan kamar hotel dengan HIL approval.
    
    Args:
        hotel_id (int): ID hotel
        check_in_date (str): Tanggal check-in dalam format YYYY-MM-DD
        check_out_date (str): Tanggal check-out dalam format YYYY-MM-DD
        jumlah_tamu (int): Jumlah tamu yang menginap
        jumlah_kamar (int): Jumlah kamar yang dipesan
        tipe_kamar (str): Tipe kamar yang dipesan
        nama_pemesan (str, optional): Nama lengkap pemesan
        email (str, optional): Alamat email pemesan
        telepon (str, optional): Nomor telepon pemesan
        user_id (int, optional): ID pengguna jika sudah terdaftar
        catatan (str, optional): Catatan tambahan untuk pemesanan
        require_approval (bool): Apakah memerlukan approval user
    
    Returns:
        String: Hasil pemesanan atau permintaan approval
    """
    if require_approval:
        # Trigger HIL approval
        approval_data = {
            "type": "hotel_booking",
            "hotel_id": hotel_id,
            "check_in_date": check_in_date,
            "check_out_date": check_out_date,
            "jumlah_tamu": jumlah_tamu,
            "jumlah_kamar": jumlah_kamar,
            "tipe_kamar": tipe_kamar,
            "nama_pemesan": nama_pemesan,
            "email": email,
            "telepon": telepon,
            "catatan": catatan,
            "message": f"Konfirmasi pemesanan hotel:\n- Hotel ID: {hotel_id}\n- Check-in: {check_in_date}\n- Check-out: {check_out_date}\n- Tamu: {jumlah_tamu}\n- Kamar: {jumlah_kamar} ({tipe_kamar})\n- Pemesan: {nama_pemesan}\n\nApakah Anda ingin melanjutkan pemesanan?"
        }
        
        # Interrupt untuk approval
        user_approval = interrupt(f"Hotel Booking Approval: {approval_data}")
        
        # Check approval
        if isinstance(user_approval, dict):
            approved = user_approval.get("approved", False)
        else:
            approved = bool(user_approval)
            
        if not approved:
            return f"Pemesanan hotel dibatalkan oleh user."
    
    # Lanjutkan dengan pemesanan asli
    return await _book_hotel_room(
        hotel_id, check_in_date, check_out_date, jumlah_tamu, jumlah_kamar, 
        tipe_kamar, nama_pemesan, email, telepon, user_id, catatan
    )

@tool
async def process_hotel_payment_hil(booking_id: int, metode_pembayaran: str, require_approval: bool = True):
    """
    Memproses pembayaran untuk pemesanan hotel dengan HIL approval.
    
    Args:
        booking_id: ID pemesanan hotel
        metode_pembayaran: Metode pembayaran ('transfer bank', 'kartu kredit', 'e-wallet')
        require_approval (bool): Apakah memerlukan approval user
    
    Returns:
        String berisi konfirmasi pembayaran atau permintaan approval
    """
    if require_approval:
        # Trigger HIL approval
        approval_data = {
            "type": "hotel_payment",
            "booking_id": booking_id,
            "metode_pembayaran": metode_pembayaran,
            "message": f"Konfirmasi pembayaran hotel:\n- Booking ID: {booking_id}\n- Metode Pembayaran: {metode_pembayaran}\n\nApakah Anda ingin melanjutkan pembayaran?"
        }
        
        # Interrupt untuk approval
        user_approval = interrupt(f"Hotel Payment Approval: {approval_data}")
        
        # Check approval
        if isinstance(user_approval, dict):
            approved = user_approval.get("approved", False)
        else:
            approved = bool(user_approval)
            
        if not approved:
            return f"Pembayaran hotel dibatalkan oleh user."
    
    # Lanjutkan dengan pembayaran asli
    return await _process_hotel_payment(booking_id, metode_pembayaran)

# List HIL tools untuk export
HIL_HOTEL_TOOLS = [
    search_hotels_by_location_hil,
    book_hotel_room_hil,
    process_hotel_payment_hil
]
