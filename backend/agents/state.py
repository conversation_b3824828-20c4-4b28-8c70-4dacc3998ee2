from typing import Annotated, Literal, Optional, Dict, Any
from typing_extensions import TypedDict
from langgraph.graph.message import AnyMessage, add_messages

def update_dialog_stack(left: list[str], right: Optional[str]) -> list[str]:
    """Push atau pop status dialog."""
    if right is None:
        return left
    if right == "pop":
        return left[:-1]
    return left + [right]

def update_hil_data(left: Optional[Dict[str, Any]], right: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """Update HIL (Human-in-the-Loop) data."""
    if right is None:
        return left
    if left is None:
        return right
    # Merge dictionaries, with right taking precedence
    merged = left.copy()
    merged.update(right)
    return merged

class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    dialog_state: Annotated[
        list[
            Literal[
                "supervisor",
                "customer_service",
                "hotel_agent",
                "flight_agent",
                "tour_agent",
            ]
        ],
        update_dialog_stack,
    ]
    user_context: Optional[Dict[str, Any]]
    # HIL (Human-in-the-Loop) fields
    hil_pending: Optional[bool]  # Flag to indicate HIL approval is pending
    hil_type: Optional[Literal["hotel_search", "hotel_booking", "hotel_payment"]]  # Type of HIL approval needed
    hil_data: Annotated[Optional[Dict[str, Any]], update_hil_data]  # Data for HIL approval (search params, booking details, etc.)
    hil_approved: Optional[bool]  # User approval status
    hil_message: Optional[str]  # Message for user approval request