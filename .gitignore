# Python environment files
.env
.venv/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock
.next/

# macOS
.DS_Store

# Supabase
supabase/supabase/apps/
supabase/supabase/examples/
supabase/agen-travel/volumes/

# LangGraph API
.langgraph_api/

# JetBrains IDE
.idea/

# Logs
logs.md

# Plan
plan.md

# Traefik sensitive files
traefik/config/.htpasswd
traefik/config/.htpasswd-api
traefik/config/.htpasswd-prometheus
traefik/config/.htpasswd-supabase
traefik/config/.htpasswd-redis
traefik/config/.env
traefik/config/users.txt
traefik/logs/*.log

# Cloudflare
.cloudflared/*.json
.cloudflared/*.log
.cloudflared/*.pem

# Human-in-the-Loop
human-in-the-loop/