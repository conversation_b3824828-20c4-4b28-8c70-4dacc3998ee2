from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from typing import List, Dict, Any, Optional
import json

def create_keyboard_from_list(
    items: List[str], 
    callback_prefix: str = "", 
    row_width: int = 2
) -> InlineKeyboardMarkup:
    """
    Create a keyboard from a list of items
    
    Args:
        items: List of items to create buttons for
        callback_prefix: Prefix for callback data
        row_width: Number of buttons per row
        
    Returns:
        InlineKeyboardMarkup
    """
    keyboard = []
    row = []
    
    for i, item in enumerate(items):
        callback_data = f"{callback_prefix}{item}" if callback_prefix else item
        row.append(InlineKeyboardButton(text=item, callback_data=callback_data))
        
        # Add row when it reaches the specified width or at the end of items
        if (i + 1) % row_width == 0 or i == len(items) - 1:
            keyboard.append(row)
            row = []
    
    return InlineKeyboardMarkup(keyboard)

def create_confirmation_keyboard(
    confirm_text: str = "✅ Ya", 
    cancel_text: str = "❌ Tidak",
    confirm_callback: str = "confirm",
    cancel_callback: str = "cancel"
) -> InlineKeyboardMarkup:
    """
    Create a simple confirmation keyboard with Yes/No buttons
    
    Args:
        confirm_text: Text for confirm button
        cancel_text: Text for cancel button
        confirm_callback: Callback data for confirm button
        cancel_callback: Callback data for cancel button
        
    Returns:
        InlineKeyboardMarkup
    """
    keyboard = [
        [
            InlineKeyboardButton(text=confirm_text, callback_data=confirm_callback),
            InlineKeyboardButton(text=cancel_text, callback_data=cancel_callback)
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def create_hil_approval_keyboard(
    approval_data: Dict[str, Any],
    thread_id: str
) -> InlineKeyboardMarkup:
    """
    Create HIL (Human-in-the-Loop) approval keyboard

    Args:
        approval_data: Data for the approval request
        thread_id: Thread ID for the conversation

    Returns:
        InlineKeyboardMarkup
    """
    # Encode approval data and thread_id in callback data
    approve_data = json.dumps({
        "action": "hil_approve",
        "thread_id": thread_id,
        "type": approval_data.get("type", "unknown")
    })

    reject_data = json.dumps({
        "action": "hil_reject",
        "thread_id": thread_id,
        "type": approval_data.get("type", "unknown")
    })

    keyboard = [
        [
            InlineKeyboardButton(text="✅ Setuju", callback_data=approve_data),
            InlineKeyboardButton(text="❌ Tolak", callback_data=reject_data)
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def create_hotel_search_approval_keyboard(
    location: str,
    thread_id: str
) -> InlineKeyboardMarkup:
    """
    Create keyboard for hotel search approval

    Args:
        location: Hotel search location
        thread_id: Thread ID for the conversation

    Returns:
        InlineKeyboardMarkup
    """
    approval_data = {
        "type": "hotel_search",
        "location": location
    }
    return create_hil_approval_keyboard(approval_data, thread_id)

def create_hotel_booking_approval_keyboard(
    booking_data: Dict[str, Any],
    thread_id: str
) -> InlineKeyboardMarkup:
    """
    Create keyboard for hotel booking approval

    Args:
        booking_data: Hotel booking data
        thread_id: Thread ID for the conversation

    Returns:
        InlineKeyboardMarkup
    """
    approval_data = {
        "type": "hotel_booking",
        **booking_data
    }
    return create_hil_approval_keyboard(approval_data, thread_id)

def create_hotel_payment_approval_keyboard(
    payment_data: Dict[str, Any],
    thread_id: str
) -> InlineKeyboardMarkup:
    """
    Create keyboard for hotel payment approval

    Args:
        payment_data: Hotel payment data
        thread_id: Thread ID for the conversation

    Returns:
        InlineKeyboardMarkup
    """
    approval_data = {
        "type": "hotel_payment",
        **payment_data
    }
    return create_hil_approval_keyboard(approval_data, thread_id)