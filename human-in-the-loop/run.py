"""
Script pembantu untuk menjalankan proyek Agent HITL

Penggunaan: python run.py [nama_contoh]
Contoh: python run.py simple
"""

import sys
import os
import subprocess
from load_env import load_environment_variables

# Daftar contoh dan file yang sesuai
EXAMPLES = {
    "simple": "simple.py",
    "notools": "notools.py",
    "autonomous": "autonomous.py",
    "centralized": "centralized.py",
    "frontend": "frontend.py",
    "backend": "backend.py",
    "frontend_rec": "frontend_rec.py",
    "backend_rec": "backend_rec.py",
    "auto_wrapper": "auto_wrapper.py",
    "auto_decorator": "auto_decorator.py",
}

def list_examples():
    """Menampilkan daftar contoh yang tersedia"""
    print("Contoh yang tersedia:")
    for name, file in EXAMPLES.items():
        print(f"  - {name}: {file}")

def run_example(example_name):
    """Menjalankan contoh yang dipilih"""
    if example_name not in EXAMPLES:
        print(f"Contoh '{example_name}' tidak ditemukan.")
        list_examples()
        return False
    
    file_path = EXAMPLES[example_name]
    if not os.path.exists(file_path):
        print(f"File '{file_path}' tidak ditemukan.")
        return False
    
    print(f"Menjalankan '{file_path}'...")
    subprocess.run([sys.executable, file_path])
    return True

def main():
    """Fungsi utama"""
    # Memuat variabel lingkungan
    if not load_environment_variables():
        print("Gagal memuat variabel lingkungan. Pastikan file .env sudah diatur dengan benar.")
        return
    
    # Memeriksa argumen
    if len(sys.argv) < 2:
        print("Penggunaan: python run.py [nama_contoh]")
        list_examples()
        return
    
    example_name = sys.argv[1].lower()
    run_example(example_name)

if __name__ == "__main__":
    main() 