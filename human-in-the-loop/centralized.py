import os
import asyncio
import uuid
from contextlib import asynccontextmanager
from typing import Annotated, Dict, List, Sequence, TypedDict, Any, Optional, Union
from load_env import load_environment_variables

from langchain_core.messages import BaseMessage, AIMessage, HumanMessage, ToolMessage,SystemMessage
from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.checkpoint.memory import MemorySaver, InMemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, tools_condition
from langgraph.types import interrupt, Command
from langchain_openai import ChatOpenAI

# Memuat variabel lingkungan
load_environment_variables()

# Membuat LLM
model = ChatOpenAI(
    model=os.getenv('OPENAI_MODEL', 'gpt-4o-mini'),
    api_key=os.getenv('OPENAI_API_KEY')
)

class State(TypedDict):
    messages: Annotated[Sequence[BaseMessage], add_messages]
    tool_calls: Optional[List[Dict[str, Any]]]
    human_approved: Optional[bool]
    approval_count: int

# Membuat grafik menggunakan konteks MCP
@asynccontextmanager
async def make_graph():
    mcp_client = MultiServerMCPClient(
            {
                "tavily-mcp": {
                    "command": "npx",
                    "args": [
                        "-y",
                        "tavily-mcp"
                    ],
                    "transport": "stdio",
                    "env": {**os.environ},
                }
            }
        )
    
    def agent(state: State):
        """Node agen LLM - memproses pesan dan menghasilkan panggilan alat atau balasan"""
        messages = state["messages"]
        response = llm_with_tool.invoke(messages)
        
        return {
            "messages": [response],
        }
    
        
    # Daftar alat berisiko tinggi
    HIGH_RISK_TOOLS = ["tavily-search", "python-repl", "shell", "file_write"]
        
    def human_approval_node(state: State):
        """Node persetujuan manusia - mendapatkan info panggilan alat dari pesan dan menunggu konfirmasi manusia"""
        # Mendapatkan panggilan alat dari pesan AI terakhir
        messages = state["messages"]
        last_message = messages[-1] if messages else None
        
        print(f"Riwayat pesan: {[msg.content for msg in messages]}")
        tool_calls = []
        if last_message and isinstance(last_message, AIMessage) and hasattr(last_message, "tool_calls") and last_message.tool_calls:
            tool_calls = last_message.tool_calls
        
        # Jika tidak ada panggilan alat, otomatis disetujui
        if not tool_calls:
            return {"human_approved": True}
        tool_calls_info = []
        
        # Mendapatkan informasi panggilan alat pertama
        tc = tool_calls[0]
        tool_id = tc.get("id", "ID Alat Tidak Dikenal")
        tool_name = tc.get("name", "Alat Tidak Dikenal")
        tool_args = tc.get("args", {})
        tool_calls_info.append(f"{tool_name}({tool_args})")
            
        print(f"Informasi panggilan alat: {tool_calls_info}")
        # Alat non-risiko tinggi disetujui otomatis
        if tool_name not in HIGH_RISK_TOOLS:
            return {"human_approved": True}
        
        tool_calls_str = "\n - ".join(tool_calls_info)
        print("\n*********Memasuki Node Persetujuan Manusia*********")
        print(f"AI ingin menggunakan alat berikut:\n - {tool_calls_str}")
        print(f"Jumlah persetujuan: {state.get('approval_count', 0)}")
        
        # Interupsi dan tunggu input manusia
        value = interrupt({
            "tool_calls": tool_calls_str,
            "message": "Masukkan 'ok' untuk menyetujui penggunaan alat, atau 'reject' untuk menolak"
        })
        
        # Perbarui hitungan persetujuan
        approval_count = state.get('approval_count', 0) + 1
        human_approved = (value.lower() == 'ok')
        
        if not human_approved:
            # Jika manusia menolak, tambahkan pesan untuk memberi tahu AI
            messages = state["messages"]
            messages.append(ToolMessage(content="Permintaan alat ditolak, silakan jawab langsung atau coba metode lain, atau tolak untuk menjawab pertanyaan.",
                                        tool_call_id=tool_id))
        
        return {
            "human_approved": human_approved,
            "approval_count": approval_count
        }

    def approval_router(state: State):
        """Menentukan langkah selanjutnya berdasarkan persetujuan manusia"""
        if state.get("human_approved"):
            # Jika disetujui, gunakan alat
            return "tools"
        else:
            # Jika tidak disetujui, kembali ke agen
            return "agent"

    mcp_tools = await mcp_client.get_tools()
    print(f"Alat yang tersedia: {[tool.name for tool in mcp_tools]}")
    llm_with_tool = model.bind_tools(mcp_tools)

    # Kompilasi dan uji aplikasi
    graph_builder = StateGraph(State)
    graph_builder.add_node("agent", agent)
    graph_builder.add_node("tool", ToolNode(mcp_tools))
    graph_builder.add_node("human_approval", human_approval_node)

    graph_builder.add_edge(START, "agent")

    # Menentukan apakah perlu menggunakan alat
    graph_builder.add_conditional_edges(
        "agent",
        tools_condition,
        {
            # Jika perlu alat, masuk ke tahap persetujuan manusia
            "tools": "human_approval", 
            END: END,
        },
    )
    
    # Menentukan langkah selanjutnya berdasarkan hasil persetujuan manusia
    graph_builder.add_conditional_edges(
        "human_approval",
        approval_router,
        {
            "tools": "tool",
            "agent": "agent"
        }
    )
    
    graph_builder.add_edge("tool", "agent")

    checkpointer = InMemorySaver()
    graph = graph_builder.compile(checkpointer=checkpointer)
    graph.name = "Agen Alat"

    yield graph

# Menjalankan proses penggunaan alat dengan keterlibatan manusia
async def run_with_hitl(query: str):
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    # Status awal
    initial_state = {
        "messages": [SystemMessage(content="Anda adalah asisten yang menggunakan alat untuk menjawab pertanyaan. Jika alat ditolak, jangan menggunakannya."), HumanMessage(content=query)], 
        "tool_calls": None, 
        "human_approved": None, 
        "approval_count": 0
    }
    
    print("=== Demo Penggunaan Alat MCP dengan Keterlibatan Manusia ===")
    
    async with make_graph() as graph:
        
        # Mulai proses
        result = await graph.ainvoke(initial_state, config=config)
        
        # Tangani kemungkinan interupsi
        while "__interrupt__" in result:
            interrupt_info = result["__interrupt__"][0].value
            print(f"\n{interrupt_info.get('message')}: ", end="")
            user_input = input()
            
            # Lanjutkan eksekusi
            result = await graph.ainvoke(
                Command(resume=user_input),
                config=config
            )
        
        # Cetak hasil akhir
        if result.get("messages"):
            print("\n=== Jawaban Akhir ===")
            print(result['messages'][-1].content)
        
        return result

# Fungsi utama
async def main():
    await run_with_hitl("Cari berita terbaru tentang Bitcoin hari ini")

# Jalankan fungsi utama
if __name__ == "__main__":
    asyncio.run(main())