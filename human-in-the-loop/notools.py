import uuid
from typing import TypedDict, Annotated, Literal, Dict, Callable, Any, Union, Optional, Sequence
from datetime import datetime
import asyncio
import os

from langgraph.checkpoint.memory import InMemorySaver
from langgraph.constants import START, END
from langgraph.graph import StateGraph
from langgraph.types import interrupt, Command
from langgraph.graph.message import add_messages
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, BaseMessage
from rich.console import Console
from rich.prompt import Prompt
from rich.panel import Panel
from rich.markdown import Markdown
from dotenv import load_dotenv

# Memuat variabel lingkungan
load_dotenv()

# Inisialisasi Rich Console
console = Console()

# Mendefinisikan tipe data interupsi
class InterruptData(TypedDict):
    # Tipe interupsi
    type: str
    
    # Deskripsi interupsi
    description: str

    # Data yang dibawa saat interupsi
    data: Dict[str, Any]

# Fungsi pembantu untuk membuat data interupsi
def create_interrupt(interrupt_type: str, description: str, data: Dict[str, Any] = None) -> InterruptData:
    """
    Membuat data interupsi yang terstandarisasi
    
    Args:
        interrupt_type: Tipe interupsi
        description: Deskripsi interupsi, ditampilkan di handle_interrupt
        data: Data tambahan
    
    Returns:
        InterruptData: Data interupsi yang terformat
    """
    return {
        "type": interrupt_type,
        "description": description,
        "data": data or {}
    }
# Mendefinisikan state
class State(TypedDict):
    # Konten teks
    text: str
    # Riwayat percakapan
    messages: Annotated[Sequence[BaseMessage], add_messages]
    # Riwayat revisi
    revision_history: list[dict]
    # Langkah saat ini
    current_step: str
    # Status selesai
    is_complete: bool
    # Petunjuk penyempurnaan
    polish_hint: str | None

# Registri penangan interupsi
interrupt_handlers = {}

# Mendaftarkan fungsi penangan interupsi
def register_interrupt_handler(interrupt_type: str, handler: Callable[[InterruptData], dict]):
    """
    Mendaftarkan penangan interupsi
    
    Args:
        interrupt_type: Tipe interupsi
        handler: Fungsi penangan, menerima interrupt_data, mengembalikan command_data
    """
    interrupt_handlers[interrupt_type] = handler

# Penangan interupsi default
def default_interrupt_handler(interrupt_data: InterruptData) -> dict:
    """
    Fungsi penangan interupsi default, untuk menangani tipe interupsi yang tidak terdaftar
    
    Args:
        interrupt_data: Data interupsi
    
    Returns:
        command_data: Data perintah setelah diproses
    """
    console.print(f"[yellow]Tipe interupsi tidak dikenal: {interrupt_data.get('type', 'Tidak diketahui')}[/yellow]")
    console.print("\n[bold yellow]Silakan masukkan respons:[/bold yellow]")
    user_input = Prompt.ask("Respons")
    return {'user_input': user_input}

# Node AI: Menganalisis teks dan memberikan saran
def ai_analysis_node(state: State):
    # Membuat LLM
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.7)
    
    # Mendapatkan petunjuk penyempurnaan (jika ada)
    polish_hint = ""
    if 'polish_hint' in state and state['polish_hint']:
        polish_hint = f"\nPetunjuk penyempurnaan: {state['polish_hint']}"
    
    analysis_prompt = f"""
    Silakan sempurnakan teks berikut agar lebih lancar, profesional dan ekspresif:
    
    Teks: {state['text']}
    -----------
    {polish_hint}
    -----------
    
    Berikan teks lengkap yang telah disempurnakan. Tanpa penjelasan lain, secara default tidak lebih dari 50 karakter.
    """
    
    # Menyiapkan pesan
    messages = state.get('messages', [])
    
    # Jika pesan kosong, buat pesan awal
    if not messages:
        messages = [HumanMessage(content=analysis_prompt)]
    else:
        # Menambahkan permintaan saat ini ke riwayat pesan
        messages.append(HumanMessage(content=analysis_prompt))
    
    # Memanggil LLM
    response = llm.invoke(messages)
    
    # Menambahkan respons LLM ke riwayat pesan
    messages.append(response)
    
    # Inisialisasi state baru
    new_state = {"messages": messages}

    # Menghapus petunjuk penyempurnaan dari putaran sebelumnya (jika ada)
    if 'polish_hint' in state:
        new_state['polish_hint'] = None

    return new_state

# Node tinjauan manusia
def human_review_node(state: State):
    # Menggunakan fungsi pembantu untuk membuat data interupsi
    interrupt_data = create_interrupt(
        interrupt_type="review",
        description="Memasuki tahap tinjauan manusia",
        data={
            "text": state['text'],
            "ai_suggestions": state['messages'][-1].content if state.get('messages') else None
        }
    )
    
    # Interupsi untuk mendapatkan input manusia, penanganan interupsi telah dipusatkan di fungsi handle_interrupt
    return interrupt(interrupt_data)

# Node konfirmasi akhir
def finalize_node(state: State):
    # Menggunakan fungsi pembantu untuk membuat data interupsi
    interrupt_data = create_interrupt(
        interrupt_type="final_confirm",
        description="Memasuki tahap konfirmasi akhir",
        data={
            "text": state['text']
        }
    )
    
    # Konfirmasi akhir, penanganan interupsi telah dipusatkan di fungsi handle_interrupt
    return interrupt(interrupt_data)

# Fungsi routing
def route_next_step(state: State) -> Union[Literal["ai_analysis", "human_review", "finalize"], str]:
    if state.get('is_complete', False):
        return END
    return state.get('current_step', 'ai_analysis')
    
# Membangun graf
def build_hitl_graph():
    graph_builder = StateGraph(State)
    
    # Menambahkan node
    graph_builder.add_node("ai_analysis", ai_analysis_node)
    graph_builder.add_node("human_review", human_review_node)
    graph_builder.add_node("finalize", finalize_node)
    
    # Menambahkan edge
    graph_builder.add_edge(START, "ai_analysis")
    
    # Setelah analisis AI langsung ke tinjauan manusia
    graph_builder.add_edge("ai_analysis", "human_review")
    
    # Routing bersyarat untuk node tinjauan manusia
    graph_builder.add_conditional_edges(
        "human_review",
        route_next_step,
        {
            "ai_analysis": "ai_analysis",
            "finalize": "finalize",
            END: END
        }
    )
    
    # Routing bersyarat untuk node konfirmasi akhir
    graph_builder.add_conditional_edges(
        "finalize",
        route_next_step,
        {
            "human_review": "human_review", 
            "ai_analysis": "ai_analysis",  # Menambahkan kemungkinan kembali ke analisis AI
            END: END
        }
    )
    
    # Mengompilasi graf
    checkpointer = InMemorySaver()
    return graph_builder.compile(checkpointer=checkpointer)

# Fungsi penanganan interupsi umum
async def handle_interrupt(graph, interrupt_result, config):
    """
    Menangani interupsi dalam alur kerja
    
    Args:
        graph: Instance LangGraph
        interrupt_result: Hasil yang berisi informasi interupsi
        config: Konfigurasi graf
    
    Returns:
        State yang telah diperbarui
    """
    if '__interrupt__' not in interrupt_result:
        return interrupt_result
    
    # Mendapatkan data interupsi dan mengkonversi ke tipe InterruptData
    interrupt_data: InterruptData = interrupt_result['__interrupt__'][0].value
    interrupt_type = interrupt_data.get('type', '')
    
    # Menampilkan pesan deskripsi interupsi
    description = interrupt_data.get('description', f'Memasuki proses penanganan interupsi {interrupt_type}')
    console.print(f"\n*****[bold cyan]{description}[/bold cyan]*****")
    
    # Mencari penangan yang sesuai
    handler = interrupt_handlers.get(interrupt_type, default_interrupt_handler)
    
    # Memanggil penangan untuk memproses interupsi
    command_data = handler(interrupt_data)
    
    # Melanjutkan eksekusi
    response = await graph.ainvoke(
        Command(resume=command_data),
        config
    )
    
    # Memeriksa apakah ada interupsi lanjutan
    if '__interrupt__' in response:
        # Memproses interupsi lanjutan secara rekursif
        return await handle_interrupt(graph, response, config)

    return response

# Fungsi menjalankan interaktif
async def run_hitl_workflow():
    graph = build_hitl_graph()
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    # Mendapatkan teks awal
    initial_text = Prompt.ask("[bold cyan]Silakan masukkan teks yang ingin disempurnakan[/bold cyan]")
    #polish_hint = Prompt.ask("[bold cyan]Silakan masukkan petunjuk penyempurnaan[/bold cyan]")
    polish_hint = 'Memperindah teks, meningkatkan ekspresi dan kelancaran'
    
    # State awal
    state = {
        "text": initial_text,
        "messages": [],
        "revision_history": [],
        "current_step": "ai_analysis",
        "is_complete": False,
        "polish_hint": polish_hint
    }
    
    try:
        result = await graph.ainvoke(state, config)
            
        # Menggunakan fungsi penanganan interupsi umum untuk menangani semua jenis interupsi
        state = await handle_interrupt(graph, result, config)
            
    except KeyboardInterrupt:
        console.print("\n[red]Proses dihentikan oleh pengguna[/red]")
    
    console.print("\n[bold green]✨ Proses selesai![/bold green]")
    console.print(f"[bold]Teks akhir：[/bold] {state.get('text', 'N/A')}")

# Mendaftarkan penangan interupsi review
def register_review_interrupt_handler():
    def review_handler(interrupt_data: InterruptData) -> dict:
        # Mendapatkan konten teks asli dari interupsi
        original_text = interrupt_data.get('data', {}).get('text', '')
        ai_suggestions = interrupt_data.get('data', {}).get('ai_suggestions', '')

        # Mencetak teks asli dan saran AI
        # console.print(Panel(f"[bold]Teks asli:[/bold]\n{original_text}", style="blue"))
        console.print(Panel(f"[bold]Teks setelah disempurnakan AI:[/bold]\n{ai_suggestions}", style="green")) 
        
        # Menampilkan pesan petunjuk dalam fungsi penangan
        console.print("\n[bold yellow]Silakan pilih tindakan:\n1. Terima teks saat ini\n2. Berikan petunjuk untuk penyempurnaan ulang\n3. Akhiri proses[/bold yellow]")
        
        choice = Prompt.ask("Pilihan", choices=['1', '2', '3'], default='1')
        command_data = {}
        
        if choice == '2':
            polish_hint = Prompt.ask("[bold cyan]Silakan masukkan petunjuk penyempurnaan (opsional, tekan Enter untuk melewati)[/bold cyan]", default="")
            if polish_hint:
                command_data.update({
                    'reason': "Menyempurnakan berdasarkan petunjuk",
                    'polish_hint': polish_hint
                })
            else:
                command_data.update({
                    'reason': "Menyempurnakan ulang" 
                })
            
            # Saat menyempurnakan ulang, langsung set langkah berikutnya ke analisis AI
            command_data['current_step'] = "ai_analysis"
        elif choice == '1':
            # Menerima teks saat ini, masuk ke tahap konfirmasi akhir
            command_data['current_step'] = "finalize"
            # Jika pengguna menerima teks saat ini, gunakan saran AI sebagai teks baru
            command_data['text'] = ai_suggestions
        elif choice == '3':
            # Mengakhiri proses
            command_data['is_complete'] = True
            command_data['current_step'] = "end"
        
        return command_data
    
    register_interrupt_handler('review', review_handler)

# Mendaftarkan penangan interupsi final_confirm
def register_final_confirm_interrupt_handler():
    def final_confirm_handler(interrupt_data: InterruptData) -> dict:
        # Mendapatkan konten teks dan riwayat revisi dari interupsi
        text = interrupt_data.get('data', {}).get('text', '')
        revision_history = interrupt_data.get('data', {}).get('revision_history', [])
        
        # Menampilkan teks akhir
        console.print(Panel(f"[bold]Teks yang disarankan AI:[/bold]\n{text}", style="green"))  
        
        # Menampilkan riwayat revisi
        if revision_history:
            console.print(Panel("[bold]Riwayat revisi:[/bold]", style="blue"))
            for revision in revision_history:
                console.print(f"- {revision.get('timestamp', '')}: {revision.get('reason', '')}")
            console.print("\n")
            
        # Menampilkan pesan petunjuk dalam fungsi penangan
        console.print("\n[bold yellow]Apakah Anda mengkonfirmasi teks akhir?[/bold yellow]")
        
        confirm = Prompt.ask("Konfirmasi", choices=['yes', 'no'], default='yes')
        
        if confirm == 'yes':
            # Pengguna mengkonfirmasi teks akhir, mengakhiri proses
            return {
                'confirmed': True,
                'is_complete': True,
                'current_step': "end",
                'text': text,  # Menyimpan teks yang dikonfirmasi
                'final_revision': {  # Menambahkan catatan revisi akhir
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'reason': "Konfirmasi akhir pengguna"
                }
            }
        else:
            # Pengguna tidak mengkonfirmasi, kembali ke tahap penyempurnaan
            return {
                'confirmed': False,
                'current_step': "human_review",
                'text': text  # Menyimpan teks saat ini
            }
    
    register_interrupt_handler('final_confirm', final_confirm_handler)

# Fungsi utama
def main():
    console.print(Panel.fit(
        "[bold cyan]Alur Kerja Pemrosesan Teks Human-in-the-Loop[/bold cyan]\n"
        "Ini adalah sistem pemrosesan teks interaktif yang menggabungkan analisis AI dan tinjauan manusia.",
        title="🤖 HITL Agent"
    ))
    
    # Mendaftarkan penangan interupsi
    register_review_interrupt_handler()
    register_final_confirm_interrupt_handler()
    asyncio.run(run_hitl_workflow())

if __name__ == "__main__":
    main()
