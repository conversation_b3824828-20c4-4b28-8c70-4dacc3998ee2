from langgraph.checkpoint.memory import InMemorySaver
from langgraph.types import interrupt
from langgraph.prebuilt import create_react_agent
import json
import asyncio
import os
from typing import Optional, Tuple, Dict, Any
from langchain_openai import ChatOpenAI
from langgraph.types import Command
from langchain_tavily import TavilySearch
from langchain_core.tools import BaseTool,Tool,tool
import requests
from load_env import load_environment_variables

# Memuat variabel lingkungan
load_environment_variables()

# Membuat LLM
model = ChatOpenAI(
    model=os.getenv('OPENAI_MODEL', 'gpt-4o-mini'),
    api_key=os.getenv('OPENAI_API_KEY')
)

# Alat pencarian Tavily, memerlukan tinjauan/persetujuan manusia
async def tavily_search(query: str, search_depth: Optional[str] = "basic"):
    """
    Melakukan pencarian web menggunakan Tavily
    
    Parameter:
    - query: kueri pencarian
    - search_depth: ked<PERSON><PERSON> pencarian, nilai yang mungkin adalah "basic" (pencarian dasar) atau "advanced" (pencarian mendalam)
    """
    # Menghentikan eksekusi, menunggu tinjauan manusia
    response = interrupt({
        "tool": "tavily_search",
        "args": {
            "query": query,
            "search_depth": search_depth
        },
        "message": f"Bersiap menggunakan pencarian Tavily:\n- Isi pencarian: {query}\n- Kedalaman pencarian: {search_depth}\n\nIzinkan melanjutkan?\nMasukkan 'yes' untuk menerima, 'no' untuk menolak, atau 'edit' untuk mengubah kata kunci pencarian",
    })   

    # Menangani respons manusia
    if response["type"] == "accept":
        pass
    elif response["type"] == "edit":
        query = response["args"]["query"]
    else:
        return f"Alat ini ditolak, silakan coba metode lain atau tolak untuk menjawab pertanyaan."
    
    # Di sini mensimulasikan hasil pencarian, dalam aplikasi sebenarnya harus memanggil API Tavily
    # Pastikan variabel lingkungan TAVILY_API_KEY ada
    # Menggunakan alat TavilySearch dari LangChain untuk pencarian sebenarnya
    try:
        search = TavilySearch(api_key=os.environ.get("TAVILY_API_KEY"))
        search_results = await search.ainvoke({
            "query": query,
            "search_depth": search_depth,
            "max_results": 5
        })
    except Exception as e:
        raise ValueError(f"Pemanggilan API Tavily gagal: {str(e)}")
    
    return json.dumps(search_results, ensure_ascii=False, indent=2)

def create_tavily_search_agent() -> Tuple[Any, Dict[str, Any]]:
    """
    Membuat agen dengan alat pencarian Tavily
    
    Returns:
        Tuple[Any, Dict[str, Any]]: tuple yang berisi (agent, config) dimana:
            - agent: instansi agen yang telah dikonfigurasi
            - config: konfigurasi agen
    """
    # Membuat penyimpan checkpoint
    checkpointer = InMemorySaver() 
    
    # Konfigurasi agen
    config = {
        "configurable": {
            "thread_id": "1"
        }
    }
    
    # Membuat agen
    agent = create_react_agent(
        model=model,
        tools=[tavily_search],
        checkpointer=checkpointer
    )
    
    return agent, config

async def main():
    """Fungsi utama, membuat dan menjalankan agen secara asinkron"""
    # Membuat agen
    agent, config = create_tavily_search_agent()
    
    # Mulai proses
    result = await agent.ainvoke(
        {"messages": [{"role": "system", "content": "Anda akan menggunakan alat untuk membantu pengguna. Jika penggunaan alat ditolak, beri tahu pengguna."},
                      {"role": "user", "content": "Bantu saya mencari berita terbaru tentang Ne Zha 3"}]},
        config=config
    )
    
    # Menangani kemungkinan interupsi
    while "__interrupt__" in result:
        interrupt_info = result["__interrupt__"][0].value
        print(f"\n{interrupt_info.get('message')}: ", end="")
        user_input = input()
        
        # Menangani input pengguna
        while True:
            if user_input.lower() == "yes":
                # Pengguna menerima teks saat ini
                result = await agent.ainvoke(
                    Command(resume={"type": "accept"}),
                    config=config
                )
                break
            elif user_input.lower() == "no":
                # Pengguna menolak
                result = await agent.ainvoke(
                    Command(resume={"type": "reject"}),
                    config=config
                )
                break
            elif user_input.lower() == "edit":
                # Pengguna memilih untuk mengedit, dapatkan konten pencarian baru
                print("Masukkan konten pencarian baru: ", end="")
                new_query = input()
                result = await agent.ainvoke(
                    Command(resume={"type": "edit", "args": {"query": new_query}}),
                    config=config
                )
                break
            else:
                # Input tidak valid, minta pengguna memasukkan ulang
                print("Input tidak valid, silakan masukkan 'yes', 'no' atau 'edit': ", end="")
                user_input = input()
    
    # Cetak hasil akhir
    if result.get("messages"):
        print("\n=== Jawaban Akhir ===")
        print(result['messages'][-1].content)
    
    return result

# Jika skrip ini dijalankan langsung, jalankan fungsi main
if __name__ == "__main__":
    # Buat loop event dan jalankan fungsi main
    asyncio.run(main())