"""
Agen Frontend Client (<PERSON><PERSON><PERSON>) - Terhubung langsung ke layanan API backend

Menggunakan mode request sinkron, konsisten dengan backend,
menyederhanakan manajemen status dan alur komunikasi, memberikan pengalaman command line yang elegan
"""
import requests
import json
import traceback
from typing import Dict, Any, Optional
import time
import uuid
from rich.console import Console
from rich.prompt import Prompt
from rich.panel import Panel
from rich.markdown import Markdown
from rich.theme import Theme
from rich.progress import Progress

# Membuat tema kustom
custom_theme = Theme({
    "info": "cyan bold",
    "warning": "yellow bold", 
    "success": "green bold",
    "error": "red bold",
    "heading": "magenta bold underline",
    "highlight": "blue bold",
})

# Inisialisasi konsol Rich
console = Console(theme=custom_theme)

# Alamat API backend
API_BASE_URL = "http://localhost:8001"

def invoke_agent(user_id: str, query: str, system_message: str = "Anda akan menggunakan alat untuk membantu pengguna. Jika penggunaan alat di<PERSON>, beri tahu pengguna."):
    """Memanggil agen untuk memproses query dan menunggu selesai atau interupsi"""
    # Mengirim request ke API backend
    payload = {
        "user_id": user_id,
        "query": query,
        "system_message": system_message
    }
    
    console.print("[info]Mengirim permintaan ke agen, mohon tunggu...[/info]")
    
    with Progress() as progress:
        task = progress.add_task("[cyan]Memproses...", total=None)
        response = requests.post(f"{API_BASE_URL}/agent/invoke", json=payload)
        progress.update(task, completed=100)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Panggilan API gagal: {response.status_code} - {response.text}")

def resume_agent(user_id: str, session_id: str, response_type: str, args: Optional[Dict[str, Any]] = None):
    """Mengirim respons untuk melanjutkan eksekusi agen"""
    payload = {
        "user_id": user_id,
        "session_id": session_id,
        "response_type": response_type,
        "args": args
    }
    
    console.print("[info]Melanjutkan eksekusi agen, mohon tunggu...[/info]")
    
    with Progress() as progress:
        task = progress.add_task("[cyan]Melanjutkan eksekusi...", total=None)
        response = requests.post(f"{API_BASE_URL}/agent/resume", json=payload)
        progress.update(task, completed=100)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Gagal melanjutkan eksekusi agen: {response.status_code} - {response.text}")

def get_agent_status(user_id: str):
    """Mendapatkan status agen"""
    response = requests.get(f"{API_BASE_URL}/agent/status/{user_id}")
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Gagal mendapatkan status agen: {response.status_code} - {response.text}")

def get_system_info():
    """Mendapatkan informasi sistem"""
    response = requests.get(f"{API_BASE_URL}/system/info")
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Gagal mendapatkan informasi sistem: {response.status_code} - {response.text}")

def delete_agent_session(user_id: str):
    """Menghapus sesi pengguna"""
    response = requests.delete(f"{API_BASE_URL}/agent/session/{user_id}")
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Gagal menghapus sesi: {response.status_code} - {response.text}")

def process_agent_response(response, user_id):
    """Memproses respons agen, termasuk menangani interupsi dan menampilkan hasil"""
    session_id = response["session_id"]
    status = response["status"]
    
    # Menampilkan ID sesi saat ini
    console.print(f"[info]ID Sesi saat ini: {session_id}[/info]")
    
    # Menangani berbagai status
    if status == "interrupted":
        # Menangani interupsi
        interrupt_data = response.get("interrupt_data", {})
        
        # Menampilkan informasi interupsi
        message = interrupt_data.get("message", "Membutuhkan input Anda")
        console.print(Panel(
            f"{message}",
            title="[warning]Agen membutuhkan keputusan Anda[/warning]",
            border_style="yellow"
        ))
        
        # Mendapatkan input pengguna
        user_input = Prompt.ask("[highlight]Pilihan Anda[/highlight]")
        
        # Memproses input pengguna
        try:
            while True:
                if user_input.lower() == "yes":
                    response = resume_agent(user_id, session_id, "accept")
                    break
                elif user_input.lower() == "no":
                    response = resume_agent(user_id, session_id, "reject")
                    break
                elif user_input.lower() == "edit":
                    # Mendapatkan konten query baru
                    new_query = Prompt.ask("[highlight]Masukkan konten pencarian baru[/highlight]")
                    response = resume_agent(user_id, session_id, "edit", args={"query": new_query})
                    break
                else:
                    console.print("[error]Input tidak valid, masukkan 'yes', 'no' atau 'edit'[/error]")
                    user_input = Prompt.ask("[highlight]Pilihan Anda[/highlight]")
            
            # Mendapatkan input pengguna kembali (mempertahankan respons saat ini)
            return process_agent_response(response, user_id)
        
        except Exception as e:
            console.print(f"[error]Kesalahan saat memproses respons: {str(e)}[/error]")
            return None
    
    elif status == "completed":
        # Menampilkan hasil
        result = response.get("result", {})
        if result and "messages" in result:
            final_message = result["messages"][-1]
            console.print(Panel(
                Markdown(final_message["content"]),
                title="[success]Jawaban Agen[/success]",
                border_style="green"
            ))
        else:
            console.print("[warning]Agen tidak mengembalikan pesan yang valid[/warning]")
        
        # Otomatis menghapus sesi setelah selesai
        try:
            delete_response = delete_agent_session(user_id)
            console.print(f"[success]Sesi telah selesai dan dibersihkan (ID Sesi: {session_id})[/success]")
        except Exception as e:
            console.print(f"[warning]Kesalahan saat menghapus sesi: {str(e)}[/warning]")
        
        return result
    
    elif status == "error":
        # Menampilkan pesan kesalahan
        error_msg = response.get("message", "Kesalahan tidak diketahui")
        console.print(Panel(
            f"{error_msg}",
            title="[error]Kesalahan selama pemrosesan[/error]",
            border_style="red"
        ))
        
        # Menghapus sesi juga dalam status error
        try:
            delete_response = delete_agent_session(user_id)
            console.print(f"[info]Sesi telah dibersihkan (ID Sesi: {session_id})[/info]")
        except Exception as e:
            console.print(f"[warning]Kesalahan saat menghapus sesi: {str(e)}[/warning]")
        
        return None
    
    else:
        # Status tidak dikenal lainnya
        console.print(f"[error]Status agen: {status} - {response.get('message', 'Tidak ada pesan')}[/error]")
        return response

def main():
    """Fungsi utama, menjalankan client"""
    console.print(Panel(
        "Agen Frontend Client (Versi Sederhana)",
        title="[heading]Sistem Interaksi Agen[/heading]",
        border_style="magenta"
    ))
    
    # Mencoba mendapatkan informasi sistem
    try:
        system_info = get_system_info()
        console.print(f"[info]Sesi sistem aktif: {system_info['sessions_count']}[/info]")
    except Exception:
        console.print("[warning]Tidak dapat mendapatkan status sistem, tapi ini tidak mempengaruhi penggunaan[/warning]")
    
    # Mendapatkan ID pengguna (dalam aplikasi nyata mungkin identitas pengguna setelah login)
    default_user_id = f"user_{int(time.time())}"
    user_id = Prompt.ask("[info]Masukkan ID pengguna[/info]", default=default_user_id)
    
    while True:
        try:
            # Mendapatkan query pengguna
            query = Prompt.ask("\n[info]Masukkan pertanyaan Anda[/info] (ketik 'exit' untuk keluar, 'status' untuk melihat status sesi)", 
                               default="Default: Halo!")
            
            if query.lower() == 'exit':
                console.print("[info]Terima kasih telah menggunakan, sampai jumpa![/info]")
                break
            elif query.lower() == 'status':
                # Melihat status sesi
                try:
                    status_info = get_agent_status(user_id)
                    console.print(Panel(
                        f"ID Pengguna: {status_info.get('user_id', 'N/A')}\n"
                        f"ID Sesi: {status_info.get('session_id', 'N/A')}\n"
                        f"Status: {status_info.get('status', 'N/A')}",
                        title="[info]Status Sesi Saat Ini[/info]",
                        border_style="cyan"
                    ))
                except Exception as e:
                    console.print(f"[warning]Gagal mendapatkan status sesi: {str(e)}[/warning]")
                continue
                
            # Memanggil agen
            response = invoke_agent(user_id, query)
            
            # Memproses respons agen
            process_agent_response(response, user_id)
            
        except KeyboardInterrupt:
            console.print("\n[warning]Interupsi pengguna, sedang keluar...[/warning]")
            break
        except Exception as e:
            console.print(f"[error]Kesalahan selama eksekusi: {str(e)}[/error]")
            console.print(traceback.format_exc())
            continue

if __name__ == "__main__":
    main()
