from langgraph.checkpoint.memory import InMemorySaver
from langgraph.types import interrupt
from langgraph.prebuilt import create_react_agent
import json
import asyncio
from typing import Optional, Tuple, Dict, Any
from langchain_openai import ChatOpenAI
from langgraph.types import Command
from langchain_tavily import <PERSON>lySearch
from langchain_core.tools import BaseTool
import os
import requests
from typing import Callable
from langchain_core.tools import BaseTool, tool as create_tool
from langchain_core.runnables import RunnableConfig
from langgraph.types import interrupt 
from langgraph.prebuilt.interrupt import HumanInterruptConfig, HumanInterrupt

def add_human_in_the_loop(
    tool: Callable | BaseTool,
    *,
    interrupt_config: HumanInterruptConfig = None,
) -> BaseTool:
    """Membungkus alat untuk mendukung tinjauan manusia.""" 
    
    if not isinstance(tool, BaseTool):
        tool = create_tool(tool)

    @create_tool(  
        tool.name,
        description=tool.description,
        args_schema=tool.args_schema
    )
    def call_tool_with_interrupt(config: RunnableConfig, **tool_input):
        request: HumanInterrupt = {
            "action_request": {
                "action": tool.name,
                "args": tool_input
            },
            "config": interrupt_config,
            "description": "Silakan tinjau pemanggilan alat berikut",
        }
        response = interrupt(request)
        print(f"response: {response}")
        
        if response["type"] == "accept":
            print("Pemanggilan alat disetujui, sedang dieksekusi...")
            print(f"Memanggil alat: {tool.name}, parameter: {tool_input}")
            try:
                tool_response = tool.invoke(input=tool_input)
                print(tool_response)
            except Exception as e:
                print(f"Pemanggilan alat gagal: {e}")

        elif response["type"] == "edit":
            tool_input = response["args"]
            try:
                tool_response = tool.invoke(input=tool_input)
                print(tool_response)
            except Exception as e:
                print(f"Pemanggilan alat gagal: {e}")
            
        elif response["type"] == "reject":
            print("Pemanggilan alat ditolak, menunggu input pengguna...")
            tool_response = 'Alat ini ditolak, silakan coba metode lain atau tolak untuk menjawab pertanyaan.'
        else:
            raise ValueError(f"Tipe respons interupsi tidak didukung: {response['type']}")

        return tool_response

    return call_tool_with_interrupt

# Membuat LLM
model = ChatOpenAI(
    model='gpt-4o-mini',
)

# Alat pencarian Tavily, memerlukan tinjauan/persetujuan manusia
def tavily_search(query: str, search_depth: Optional[str] = "basic"):
    """
    Melakukan pencarian web menggunakan Tavily
    
    Parameter:
    - query: kueri pencarian
    - search_depth: kedalaman pencarian, nilai yang mungkin adalah "basic" (pencarian dasar) atau "advanced" (pencarian mendalam)
    """
    try:
        search = TavilySearch(api_key=os.environ.get("TAVILY_API_KEY"))
        search_results = search.invoke({
            "query": query,
            "search_depth": search_depth,
            "max_results": 5
        })
    except Exception as e:
        raise ValueError(f"Pemanggilan API Tavily gagal: {str(e)}")
    
    return json.dumps(search_results, ensure_ascii=False, indent=2)

def create_tavily_search_agent() -> Tuple[Any, Dict[str, Any]]:
    """
    Membuat agen dengan alat pencarian Tavily
    
    Returns:
        Tuple[Any, Dict[str, Any]]: tuple yang berisi (agent, config) dimana:
            - agent: instansi agen yang telah dikonfigurasi
            - config: konfigurasi agen
    """
    # Membuat penyimpan checkpoint
    checkpointer = InMemorySaver() 
    
    # Konfigurasi agen
    config = {
        "configurable": {
            "thread_id": "1"
        }
    }
    
    # Membuat agen
    agent = create_react_agent(
        model=model,
        tools=[add_human_in_the_loop(tavily_search)],
        checkpointer=checkpointer
    )
    
    return agent, config

async def main():
    """Fungsi utama, membuat dan menjalankan agen secara asinkron"""
    # Membuat agen
    agent, config = create_tavily_search_agent()
    
    # Memulai proses
    result =  agent.invoke(
        {"messages": [{"role": "system", "content": "Anda akan menggunakan alat untuk membantu pengguna. Jika penggunaan alat ditolak, beri tahu pengguna."},
                      {"role": "user", "content": "Tolong cari berita terbaru tentang Ne Zha 3"}]},
        config=config
    )
    
    # Menangani kemungkinan interupsi
    while "__interrupt__" in result:
        interrupt_info = result["__interrupt__"][0].value
        print(f"\n{interrupt_info.get('description')}: ", end="")
        print(f"Permintaan alat: {interrupt_info.get('action_request', {}).get('action')}")
        print(f"Parameter permintaan: {interrupt_info.get('action_request', {}).get('args')}")
        print("Masukkan 'yes' untuk menerima, 'no' untuk menolak, atau 'edit' untuk mengubah kata kunci pencarian: ", end="")
        user_input = input()
        
        # Menangani input pengguna
        while True:
            if user_input.lower() == "yes":
                # Pengguna menerima teks saat ini
                result = await agent.ainvoke(
                    Command(resume={"type": "accept"}),
                    config=config
                )
                break
            elif user_input.lower() == "no":
                # Pengguna menolak
                result = await agent.ainvoke(
                    Command(resume={"type": "reject"}),
                    config=config
                )
                break
            elif user_input.lower() == "edit":
                # Pengguna memilih untuk mengedit, dapatkan konten pencarian baru
                print("Masukkan konten pencarian baru: ", end="")
                new_query = input()
                result = await agent.ainvoke(
                    Command(resume={"type": "edit", "args": {"query": new_query}}),
                    config=config
                )
                break
            else:
                # Input tidak valid, minta pengguna memasukkan ulang
                print("Input tidak valid, silakan masukkan 'yes', 'no', atau 'edit': ", end="")
                user_input = input()
    
    # Mencetak hasil akhir
    if result.get("messages"):
        print("\n=== Jawaban Akhir ===")
        print(result['messages'][-1].content)
    
    return result

# Jika skrip ini dijalankan langsung, jalankan fungsi main
if __name__ == "__main__":
    # Membuat loop event dan menjalankan fungsi main
    asyncio.run(main())