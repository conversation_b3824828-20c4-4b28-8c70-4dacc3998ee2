# Agent HITL (Human-in-the-Loop)

Sistem agen AI yang mendukung kolaborasi manusia-me<PERSON>, memungkinkan intervensi dan pengawasan manusia dalam proses pengambilan keputusan AI. <PERSON><PERSON><PERSON>, LangGraph dan FastAPI.

## Fitur Utama

- 🤖 Sistem Agen <PERSON> (berbasis LangChain/LangGraph)
- 👥 Mode Kolaborasi Manusia-Mesin (Human-in-the-Loop)
- 🔄 Antarmuka Interaktif Real-time (Backend FastAPI)
- 📊 Visualisasi Proses Pengambilan Keputusan
- 🛡️ Mekanisme Kontrol Keamanan
- 🛠️ Berbagai Mode Integrasi Alat

## Memulai

### Persyaratan Sistem

- Python >= 3.12
- uv (direkomendasikan) atau pip

### Instalasi Dependensi

Menggunakan uv (direkomendasikan):
```bash
uv sync
```

Atau menggunakan pip:
```bash
python install_dependencies.py
```

### Menjalankan Proyek

Gunakan script helper untuk menjalankan berbagai contoh dengan mudah:

```bash
python run.py [nama_contoh]
```

Contoh yang tersedia:

```bash
# Contoh dasar (tanpa alat)
python run.py notools

# Contoh alat sederhana
python run.py simple

# Mode otonom
python run.py autonomous

# Pengelolaan alat terpusat
python run.py centralized

# Alat fungsi Frontend/Backend (perlu dijalankan di dua terminal terpisah)
python run.py backend
python run.py frontend

# Eksekusi alat yang dapat dipulihkan
python run.py backend_rec
python run.py frontend_rec

# Mode lainnya
python run.py auto_wrapper
python run.py auto_decorator
```

## Struktur Proyek

```
agent_hitl/
├── simple.py                   # Contoh alat sederhana
├── notools.py                  # Contoh dasar (tanpa alat)
├── autonomous.py               # Mode otonom
├── centralized.py              # Pengelolaan alat terpusat
├── frontend.py                 # Antarmuka frontend
├── backend.py                  # Server backend API
├── frontend_rec.py             # Frontend dengan kemampuan pemulihan
├── backend_rec.py              # Backend dengan kemampuan pemulihan
├── auto_wrapper.py             # Implementasi wrapper otonom
├── auto_decorator.py           # Implementasi decorator otonom
├── run.py                      # Script helper untuk menjalankan contoh
├── load_env.py                 # Loader variabel lingkungan
├── install_dependencies.py     # Skrip instalasi dependensi
├── pyproject.toml              # Konfigurasi proyek Python
├── uv.lock                     # File penguncian dependensi
└── README.md                   # Dokumentasi proyek
```

### Variabel Lingkungan

Buat file `.env` dengan konfigurasi berikut:

```
# API Keys
OPENAI_API_KEY=your_openai_api_key
TAVILY_API_KEY=your_tavily_api_key

# Model Configuration
OPENAI_MODEL=gpt-4o-mini

# PostgreSQL Configuration (opsional, untuk backend)
POSTGRES_CONNECTION_STRING=postgresql://username:password@localhost/database
```

## Dependensi Utama

- **FastAPI**: Framework Web
- **LangChain**: Framework Pengembangan AI
- **LangGraph**: Alur Kerja Diagram Status
- **LangChain-OpenAI**: Integrasi OpenAI  
- **Rich**: Output Terminal yang Indah
- **Python-dotenv**: Pengelolaan Variabel Lingkungan

## Panduan Kontribusi

Kami menyambut Issue dan Pull Request untuk meningkatkan proyek ini.

## Lisensi

MIT License