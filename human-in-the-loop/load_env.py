import os
from dotenv import load_dotenv

def load_environment_variables():
    """
    Memuat variabel lingkungan dari file .env
    Mengembalikan True jika berhasil, False jika gagal
    """
    try:
        # Memuat variabel lingkungan dari file .env
        load_dotenv()
        
        # Memeriksa variabel lingkungan penting
        required_vars = ["OPENAI_API_KEY", "TAVILY_API_KEY"]
        missing_vars = [var for var in required_vars if not os.environ.get(var)]
        
        if missing_vars:
            print(f"Peringatan: Variabel lingkungan berikut tidak ditemukan: {', '.join(missing_vars)}")
            print("Pastikan Anda telah mengatur semua variabel yang diperlukan di file .env")
            return False
        
        print("Variabel lingkungan berhasil dimuat!")
        return True
    except Exception as e:
        print(f"Gagal memuat variabel lingkungan: {e}")
        return False

if __name__ == "__main__":
    load_environment_variables() 