"""
Instal paket dependensi yang diperlukan
"""

import subprocess
import sys
import os

def install_dependencies():
    """Instal semua dependensi yang diperlukan untuk proyek"""
    packages = [
        "fastapi",
        "uvicorn", 
        "pydantic",
        "aiohttp",
        "rich",
        "langchain",
        "langchain-openai",
        "langgraph",
        "langchain-tavily",
        "python-dotenv"
    ]
    
    print("Sedang menginstal dependensi...")
    subprocess.check_call([sys.executable, "-m", "pip", "install"] + packages)
    print("Instalasi dependensi selesai!")

if __name__ == "__main__":
    install_dependencies()
