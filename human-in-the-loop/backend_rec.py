"""
Layanan Backend Agent - Menyediakan API berbasis FastAPI (versi sederhana)

Menyediakan layanan agen dengan mode pemrosesan sinkron, langsung mengembalikan hasil atau data interupsi,
menyederhanakan manajemen status dan alur komunikasi
"""
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List, Literal
import json
import uuid
import time

from langgraph.checkpoint.memory import InMemorySaver
from langgraph.types import interrupt, Command
from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI
from langchain_tavily import TavilySearch
import os
import uvicorn

# Penyimpanan sesi - menyimpan instance agen dan status untuk setiap pengguna
# Sementara hanya mendukung satu sesi per pengguna, tidak bisa multi sesi per pengguna
# Struktur: {user_id: {
#   "agent": agent,  
#   "session_id": session_id, 
#   "status": "idle|running|interrupted|completed|error",
#   "last_response": AgentResponse,     # Respon terakhir
#   "last_query": str,                  # Query terakhir
#   "last_updated": timestamp           # Waktu update terakhir
# }}
sessions = {}

# Membuat LLM
model = ChatOpenAI(model="gpt-4o-mini")

# Mendefinisikan manajer konteks siklus hidup
from contextlib import asynccontextmanager
from langgraph.checkpoint.postgres import PostgresSaver

@asynccontextmanager
async def lifespan(app: FastAPI):
    
    # Inisialisasi penyimpan checkpoint PostgreSQL
    with PostgresSaver.from_conn_string("postgresql://postgres:yourpassword@localhost/postgres?sslmode=disable") as checkpointer:
        checkpointer.setup()

        # Menetapkan penyimpan checkpoint sebagai akses global
        app.state.checkpointer = checkpointer

        yield

# Menginisialisasi aplikasi dengan manajer konteks siklus hidup
app = FastAPI(
    title="Layanan Backend Agent", 
    description="Menyediakan layanan AI Agent berbasis LangGraph",
    lifespan=lifespan
)

# Mendefinisikan model request dan response
class AgentRequest(BaseModel):
    user_id: str
    query: str
    system_message: Optional[str] = "Anda akan menggunakan alat untuk membantu pengguna. Jika penggunaan alat ditolak, harap beri tahu pengguna."

class AgentResponse(BaseModel):
    session_id: str
    status: str  # interrupted, completed, error, running, idle
    timestamp: float = Field(default_factory=lambda: time.time())

    #error
    message: Optional[str] = None

    #completed
    result: Optional[Dict[str, Any]] = None

    #interrupted
    interrupt_data: Optional[Dict[str, Any]] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "interrupted",
                "timestamp": 1621500000.123,
                "interrupt_data": {
                    "interrupt_type": "tool_use_approval",
                    "tool": "tavily_search",
                    "args": {"query": "informasi terbaru Ne Zha 3", "search_depth": "basic"},
                    "message": "Bersiap menggunakan pencarian Tavily...",
                    "expected_responses": ["accept", "reject", "edit"]
                }
            }
        }

class InterruptResponse(BaseModel):
    user_id: str
    session_id: str
    response_type: str  # accept, reject, edit
    args: Optional[Dict[str, Any]] = None

class SystemInfoResponse(BaseModel):
    sessions_count: int
    active_users: List[str]
    
# Fungsi pembantu untuk memproses hasil agen
def process_agent_result(
    session_id: str, 
    result: Dict[str, Any],
    user_id: Optional[str] = None
) -> AgentResponse:
    """
    Memproses hasil eksekusi agen, menangani interupsi dan hasil secara terpadu
    
    Args:
        session_id: ID sesi
        result: Hasil eksekusi agen
        user_id: ID pengguna, jika disediakan akan memperbarui status sesi
        
    Returns:
        AgentResponse: Objek respon terstandarisasi
    """
    response = None

    try:
        # Memeriksa apakah ada interupsi
        if "__interrupt__" in result:
            interrupt_data = result["__interrupt__"][0].value
            
            # Memastikan data interupsi memiliki informasi tipe
            if "interrupt_type" not in interrupt_data:
                interrupt_data["interrupt_type"] = "unknown"
            
            # Mengembalikan informasi interupsi
            response = AgentResponse(
                session_id=session_id,
                status="interrupted",
                interrupt_data=interrupt_data
            )
        else:
            # Jika tidak ada interupsi, mengembalikan hasil akhir
            response = AgentResponse(
                session_id=session_id,
                status="completed",
                result=result
            )
        
    except Exception as e:
        response = AgentResponse(
            session_id=session_id,
            status="error",
            message=f"Terjadi kesalahan saat memproses hasil agen: {str(e)}"
        )
        
    # Jika ID pengguna disediakan, perbarui status sesi
    if user_id and user_id in sessions:
        sessions[user_id]["status"] = response.status
        sessions[user_id]["last_response"] = response
        sessions[user_id]["last_updated"] = time.time()
        
    return response

# Alat pencarian Tavily, memerlukan persetujuan/peninjauan manual
def tavily_search(query: str, search_depth: Optional[str] = "basic"):
    """
    Melakukan pencarian web menggunakan Tavily
    
    Parameter:
    - query: Query pencarian
    - search_depth: Kedalaman pencarian, pilihan "basic" (pencarian dasar) atau "advanced" (pencarian mendalam)
    """
    # Interupsi eksekusi, menunggu persetujuan manual
    response = interrupt({
        "interrupt_type": "tool_use_approval",
        "tool": "tavily_search",
        "args": {
            "query": query,
            "search_depth": search_depth
        },
        "message": f"Bersiap menggunakan pencarian Tavily:\n- Konten pencarian: {query}\n- Kedalaman pencarian: {search_depth}\n\nIzinkan melanjutkan?\nKetik 'yes' untuk menerima, 'no' untuk menolak, atau 'edit' untuk mengubah kata kunci pencarian",
        "expected_responses": ["accept", "reject", "edit"]
    })   

    # Menangani respon manual
    if response["type"] == "accept":
        pass
    elif response["type"] == "edit":
        query = response["args"]["query"]
    else:
        return f"Penggunaan alat ini ditolak, silakan coba cara lain atau tolak menjawab pertanyaan."
    
    # Melakukan pencarian aktual menggunakan alat TavilySearch dari LangChain
    try:
        search = TavilySearch(api_key=os.environ.get("TAVILY_API_KEY"))
        search_results = search.invoke({
            "query": query,
            "search_depth": search_depth,
            "max_results": 5
        })
    except Exception as e:
       return f"Pemanggilan alat gagal: {str(e)}"
    
    return json.dumps(search_results, ensure_ascii=False, indent=2)

def create_tavily_search_agent(user_id: str):
    """
    Membuat agen dengan alat pencarian Tavily
    
    Args:
        user_id: ID pengguna, digunakan untuk mengisolasi sesi antar pengguna
        
    Returns:
        agent: Instance agen yang telah dikonfigurasi
    """
    # Membuat agen
    agent = create_react_agent(
        model=model,
        tools=[tavily_search],
        checkpointer=app.state.checkpointer,  # Menggunakan penyimpan checkpoint global
    )
    
    return agent

@app.post("/agent/invoke", response_model=AgentResponse)
def invoke_agent(request: AgentRequest):
    """Memulai pemrosesan permintaan pengguna oleh agen - versi sinkron, menunggu selesai atau interupsi"""
    user_id = request.user_id
    
    # Memeriksa apakah sesi sudah ada, jika tidak buat sesi baru
    if user_id not in sessions:
        # Hanya generate ID sesi baru saat membuat sesi baru
        session_id = str(uuid.uuid4())
        agent = create_tavily_search_agent(user_id)
        sessions[user_id] = {
            "agent": agent,
            "session_id": session_id,
            "status": "idle",
            "last_query": None,
            "last_response": None,
            "last_updated": time.time()
        }
    else:
        # Menggunakan ID dan agent dari sesi yang ada
        agent = sessions[user_id]["agent"]
        session_id = sessions[user_id]["session_id"]
    
    # Memperbarui informasi sesi untuk permintaan baru
    sessions[user_id]["status"] = "running"
    sessions[user_id]["last_query"] = request.query
    sessions[user_id]["last_response"] = None
    sessions[user_id]["last_updated"] = time.time()
    
    # Inisialisasi input agen
    messages = [
        {"role": "system", "content": request.system_message},
        {"role": "user", "content": request.query}
    ]
    
    try:
        # Memanggil agen terlebih dahulu
        result = agent.invoke({"messages": messages}, config={"configurable": {"thread_id": session_id}})

        # Kemudian memproses hasil dan memperbarui status sesi
        return process_agent_result(session_id, result, user_id)
    
    except Exception as e:
        # Penanganan kesalahan
        error_response = AgentResponse(
            session_id=session_id,
            status="error",
            message=f"Terjadi kesalahan saat memproses permintaan: {str(e)}"
        )
        
        # Memperbarui status sesi
        sessions[user_id]["status"] = "error"
        sessions[user_id]["last_response"] = error_response
        sessions[user_id]["last_updated"] = time.time()
        
        return error_response

@app.post("/agent/resume", response_model=AgentResponse)
def resume_agent(response: InterruptResponse):
    """Melanjutkan eksekusi agen yang terinterupsi - versi sinkron, menunggu selesai atau interupsi kembali"""
    user_id = response.user_id
    client_session_id = response.session_id
    
    # Memeriksa apakah sesi pengguna ada
    if user_id not in sessions:
        raise HTTPException(status_code=404, detail=f"Sesi pengguna {user_id} tidak ditemukan")
    
    # Memeriksa apakah ID sesi cocok
    server_session_id = sessions[user_id]["session_id"]
    if server_session_id != client_session_id:
        raise HTTPException(status_code=400, detail="ID sesi tidak cocok, kemungkinan permintaan sudah kadaluarsa")
    
    # Memeriksa apakah status sesi adalah terinterupsi
    if sessions[user_id]["status"] != "interrupted":
        raise HTTPException(status_code=400, detail=f"Status sesi saat ini adalah {sessions[user_id]['status']}, tidak dapat melanjutkan sesi yang tidak terinterupsi")
    
    # Memperbarui status sesi menjadi berjalan
    sessions[user_id]["status"] = "running"
    sessions[user_id]["last_updated"] = time.time()
    
    # Mendapatkan agen dan konfigurasi
    agent = sessions[user_id]["agent"]
    
    # Menyusun data respon
    command_data = {
        "type": response.response_type
    }
    
    # Jika parameter disediakan, tambahkan ke data respon
    if response.args:
        command_data["args"] = response.args
    
    try:
        # Melanjutkan eksekusi agen terlebih dahulu
        result = agent.invoke(Command(resume=command_data), config={"configurable": {"thread_id": server_session_id}})

        # Kemudian memproses hasil dan memperbarui status sesi
        return process_agent_result(server_session_id, result, user_id)
    
    except Exception as e:
        # Penanganan kesalahan
        error_response = AgentResponse(
            session_id=server_session_id,
            status="error",
            message=f"Terjadi kesalahan saat melanjutkan eksekusi: {str(e)}"
        )
        
        # Memperbarui status sesi
        sessions[user_id]["status"] = "error"
        sessions[user_id]["last_response"] = error_response
        sessions[user_id]["last_updated"] = time.time()
        
        return error_response

class SessionStatusResponse(BaseModel):
    user_id: str
    session_id: Optional[str] = None
    status: str  # not_found, idle, running, interrupted, completed, error
    message: Optional[str] = None
    last_query: Optional[str] = None
    last_updated: Optional[float] = None
    last_response: Optional[AgentResponse] = None

@app.get("/agent/status/{user_id}", response_model=SessionStatusResponse)
def get_agent_status(user_id: str):
    """Mendapatkan status sesi agen pengguna"""
    if user_id not in sessions:
        return SessionStatusResponse(
            user_id=user_id,
            status="not_found",
            message=f"Sesi untuk pengguna {user_id} tidak ditemukan"
        )
    
    session = sessions[user_id]
    return SessionStatusResponse(
        user_id=user_id,
        session_id=session["session_id"],
        status=session["status"],
        last_query=session["last_query"],
        last_updated=session["last_updated"],
        last_response=session["last_response"]
    )

@app.get("/system/info", response_model=SystemInfoResponse)
def get_system_info():
    """Mendapatkan informasi status sistem"""
    return SystemInfoResponse(
        sessions_count=len(sessions),
        active_users=list(sessions.keys())
    )

@app.delete("/agent/session/{user_id}")
def delete_agent_session(user_id: str):
    """Menghapus sesi pengguna"""
    if user_id not in sessions:
        raise HTTPException(status_code=404, detail=f"Sesi untuk pengguna {user_id} tidak ditemukan")
    
    # Menghapus sesi
    del sessions[user_id]
    
    return {
        "status": "success",
        "message": f"Sesi pengguna {user_id} telah dihapus"
    }

# Menjalankan server
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
