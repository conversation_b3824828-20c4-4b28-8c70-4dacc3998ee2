"""
Layanan backend Agent - Menyediakan API berbasis FastAPI (versi sederhana)

Menyediakan layanan proxy dengan mode pem<PERSON>san sinkron, langsung mengembalikan hasil atau data interupsi,
menyederhanakan manajemen status dan alur komunikasi
"""
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import json
import uuid

from langgraph.checkpoint.memory import InMemorySaver
from langgraph.types import interrupt, Command
from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI
from langchain_tavily import TavilySearch
import os
import uvicorn
from load_env import load_environment_variables

# Memuat variabel lingkungan
load_environment_variables()

# Penyimpanan sesi - menyimpan instance dan status agen untuk setiap pengguna
# Struktur: {user_id: 
#           {"agent": agent, 
#            "session_id": session_id}
#       }
sessions = {}

# Membuat LLM
model = ChatOpenAI(
    model=os.getenv('OPENAI_MODEL', 'gpt-4o-mini'),
    api_key=os.getenv('OPENAI_API_KEY')
)

# Mendefinisikan manajer konteks siklus hidup
from contextlib import asynccontextmanager
from langgraph.checkpoint.postgres import PostgresSaver

@asynccontextmanager
async def lifespan(app: FastAPI):
    
    # Inisialisasi penyimpan checkpoint PostgreSQL
    with PostgresSaver.from_conn_string("postgresql://postgres:yourpassword@localhost/postgres?sslmode=disable") as checkpointer:
        checkpointer.setup()

        # Mengatur penyimpan checkpoint agar dapat diakses secara global
        app.state.checkpointer = checkpointer

        yield

# Inisialisasi aplikasi dengan manajer konteks siklus hidup
app = FastAPI(
    title="Layanan Backend Agent", 
    description="Menyediakan layanan AI Agent berbasis LangGraph",
    lifespan=lifespan
)

# Permintaan agen dari klien
class AgentRequest(BaseModel):
    user_id: str
    query: str
    system_message: Optional[str] = "Anda akan menggunakan alat untuk membantu pengguna. Jika penggunaan alat ditolak, harap beri tahu pengguna."

# Respons dari agen
class AgentResponse(BaseModel):

    # Pengidentifikasi unik untuk satu sesi
    session_id: str

    # Tiga status: interrupted, completed, error
    status: str  

    # Pesan ketika terjadi error
    message: Optional[str] = None

    # Pesan hasil ketika completed
    result: Optional[Dict[str, Any]] = None

    # Pesan interupsi ketika interrupted
    interrupt_data: Optional[Dict[str, Any]] = None

# Respons umpan balik dari klien
class InterruptResponse(BaseModel):
    user_id: str
    session_id: str

    # Tipe respons: accept, reject, edit
    response_type: str  

    # Parameter tambahan jika tipe edit
    args: Optional[Dict[str, Any]] = None

# Respons informasi sistem
class SystemInfoResponse(BaseModel):
    sessions_count: int
    active_users: List[str]
    
# Memproses hasil agen: bisa berupa interupsi atau hasil akhir
def process_agent_result(
    session_id: str, 
    result: Dict[str, Any]
) -> AgentResponse:
    """
    Memproses hasil eksekusi agen, menangani interupsi dan hasil secara terpadu
    
    Args:
        result: Hasil eksekusi agen
        session_id: ID sesi
        
    Returns:
        AgentResponse: Objek respons terstandarisasi
    """
    try:
        # Memeriksa apakah ada interupsi
        if "__interrupt__" in result:
            interrupt_data = result["__interrupt__"][0].value
            
            # Mengembalikan informasi interupsi
            return AgentResponse(
                session_id=session_id,
                status="interrupted",
                interrupt_data=interrupt_data
            )
        
        # Jika tidak ada interupsi, mengembalikan hasil akhir
        return AgentResponse(
            session_id=session_id,
            status="completed",
            result=result
        )
    
    except Exception as e:
        return AgentResponse(
            session_id=session_id,
            status="error",
            message=f"Terjadi kesalahan saat memproses hasil agen: {str(e)}"
        )

# Alat pencarian Tavily, memerlukan persetujuan manual
def tavily_search(query: str, search_depth: Optional[str] = "basic"):
    """
    Melakukan pencarian web menggunakan Tavily
    
    Parameter:
    - query: Kueri pencarian
    - search_depth: Kedalaman pencarian, nilai yang mungkin adalah "basic" (pencarian dasar) atau "advanced" (pencarian mendalam)
    """
    # Menghentikan eksekusi, menunggu persetujuan manual
    response = interrupt({
        "tool": "tavily_search",
        "args": {
            "query": query,
            "search_depth": search_depth
        },
        "message": f"Bersiap menggunakan pencarian Tavily:\n- Isi pencarian: {query}\n- Kedalaman pencarian: {search_depth}\n\nApakah diizinkan untuk melanjutkan?\nMasukkan 'yes' untuk menerima, 'no' untuk menolak, atau 'edit' untuk mengubah kata kunci pencarian",
    })   

    # Menangani respons manual
    if response["type"] == "accept":
        pass
    elif response["type"] == "edit":
        query = response["args"]["query"]
    else:
        return f"Penggunaan alat ini ditolak, silakan coba metode lain atau tolak untuk menjawab pertanyaan."
    
    # Melakukan pencarian aktual menggunakan alat TavilySearch dari LangChain
    try:
        search = TavilySearch(api_key=os.environ.get("TAVILY_API_KEY"))
        search_results = search.invoke({
            "query": query,
            "search_depth": search_depth,
            "max_results": 5
        })
    except Exception as e:
       return f"Pemanggilan alat gagal: {str(e)}"
    
    return json.dumps(search_results, ensure_ascii=False, indent=2)

# Membuat agen: React Agent
def create_tavily_search_agent(user_id: str):

    agent = create_react_agent(
        model=model,
        tools=[tavily_search],
        checkpointer=app.state.checkpointer,  # Menggunakan penyimpan checkpoint global
    )
    
    return agent

# API terbuka: Membuat dan memanggil agen
# Catatan: Ini adalah versi sinkron, langsung mengembalikan hasil atau data interupsi
@app.post("/agent/invoke", response_model=AgentResponse)
def invoke_agent(request: AgentRequest):
    """Memulai pemrosesan permintaan pengguna oleh agen - versi sinkron, menunggu selesai atau interupsi"""
    user_id = request.user_id
    
    # Memeriksa apakah sesi sudah ada, jika tidak membuat sesi baru
    if user_id not in sessions:

        # Hanya menghasilkan ID sesi baru saat membuat sesi baru
        session_id = str(uuid.uuid4())
        agent = create_tavily_search_agent(user_id)
        sessions[user_id] = {
            "agent": agent,
            "session_id": session_id
        }
    else:
        # Menggunakan ID dan agen dari sesi yang ada
        agent = sessions[user_id]["agent"]
        session_id = sessions[user_id]["session_id"]
        
    # Inisialisasi input agen
    messages = [
        {"role": "system", "content": request.system_message},
        {"role": "user", "content": request.query}
    ]
    
    try:

        # Pertama memanggil agen
        result = agent.invoke({"messages": messages},config={"configurable": {"thread_id": session_id}})

        # Kemudian memproses hasil
        return process_agent_result(session_id, result)
    
    except Exception as e:
        # Penanganan kesalahan
        return AgentResponse(
            session_id=session_id,
            status="error",
            message=f"Terjadi kesalahan saat memproses permintaan: {str(e)}"
        )

# API terbuka: Melanjutkan eksekusi agen yang terinterupsi
# Catatan: Ini adalah versi sinkron, menunggu selesai atau interupsi kembali
@app.post("/agent/resume", response_model=AgentResponse)
def resume_agent(response: InterruptResponse):
    """Melanjutkan eksekusi agen yang terinterupsi - versi sinkron, menunggu selesai atau interupsi kembali"""
    user_id = response.user_id
    client_session_id = response.session_id
    
    # Memeriksa apakah sesi pengguna ada
    if user_id not in sessions:
        raise HTTPException(status_code=404, detail=f"Sesi pengguna {user_id} tidak ditemukan")
    
    # Memeriksa apakah ID sesi cocok
    server_session_id = sessions[user_id]["session_id"]
    if server_session_id != client_session_id:
        raise HTTPException(status_code=400, detail="ID sesi tidak cocok, mungkin permintaan sudah kedaluwarsa")
    
    # Mendapatkan agen dan konfigurasi
    agent = sessions[user_id]["agent"]
    
    # Menyusun data respons
    command_data = {
        "type": response.response_type
    }
    
    # Jika ada parameter, tambahkan ke data respons
    if response.args:
        command_data["args"] = response.args
    
    try:
        # Pertama melanjutkan eksekusi agen
        result = agent.invoke(Command(resume=command_data), config={"configurable": {"thread_id": server_session_id}})

        # Kemudian memproses hasil
        return process_agent_result(server_session_id, result)
    
    except Exception as e:
        # Penanganan kesalahan
        return AgentResponse(
            session_id=server_session_id,
            status="error",
            message=f"Terjadi kesalahan saat melanjutkan eksekusi: {str(e)}"
        )

####################################################
# API pembantu lainnya di bawah ini
####################################################
@app.get("/agent/status/{user_id}")
def get_agent_status(user_id: str):
    """Mendapatkan status sesi agen pengguna"""
    if user_id not in sessions:
        return {
            "status": "not_found",
            "message": f"Sesi untuk pengguna {user_id} tidak ditemukan"
        }
    
    session = sessions[user_id]
    return {
        "user_id": user_id,
        "session_id": session["session_id"],
        "status": "active"
    }

@app.get("/system/info", response_model=SystemInfoResponse)
def get_system_info():
    """Mendapatkan informasi status sistem"""
    return SystemInfoResponse(
        sessions_count=len(sessions),
        active_users=list(sessions.keys())
    )

@app.delete("/agent/session/{user_id}")
def delete_agent_session(user_id: str):
    """Menghapus sesi pengguna"""
    if user_id not in sessions:
        raise HTTPException(status_code=404, detail=f"Sesi untuk pengguna {user_id} tidak ditemukan")
    
    # Menghapus sesi
    del sessions[user_id]
    
    return {
        "status": "success",
        "message": f"Sesi untuk pengguna {user_id} telah dihapus"
    }

# Menjalankan server
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
