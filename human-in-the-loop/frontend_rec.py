"""
Klien frontend Agent (versi sederhana) - Terhu<PERSON>ng langsung ke layanan API backend

Menggunakan mode request sinkron, konsisten dengan backend,
menyederhanakan manajemen status dan alur komunikasi, memberikan pengalaman command line yang elegan
"""
import requests
import json
import traceback
from typing import Dict, Any, Optional
import time
import uuid
from rich.console import Console
from rich.prompt import Prompt
from rich.panel import Panel
from rich.markdown import Markdown
from rich.theme import Theme
from rich.progress import Progress

# Membuat tema kustom
custom_theme = Theme({
    "info": "cyan bold",
    "warning": "yellow bold", 
    "success": "green bold",
    "error": "red bold",
    "heading": "magenta bold underline",
    "highlight": "blue bold",
})

# Inisialisasi konsol Rich
console = Console(theme=custom_theme)

# Alamat API backend
API_BASE_URL = "http://localhost:8001"

def invoke_agent(user_id: str, query: str, system_message: str = "Anda akan menggunakan alat untuk membantu pengguna. Jika penggunaan alat ditolak, harap beri tahu pengguna."):
    """Memanggil agen untuk memproses query dan menunggu selesai atau interupsi"""
    # Mengirim request ke API backend
    payload = {
        "user_id": user_id,
        "query": query,
        "system_message": system_message
    }
    
    console.print("[info]Mengirim permintaan ke agen, mohon tunggu...[/info]")
    
    with Progress() as progress:
        task = progress.add_task("[cyan]Memproses...", total=None)
        response = requests.post(f"{API_BASE_URL}/agent/invoke", json=payload)
        progress.update(task, completed=100)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Panggilan API gagal: {response.status_code} - {response.text}")

def resume_agent(user_id: str, session_id: str, response_type: str, args: Optional[Dict[str, Any]] = None):
    """Mengirim respons untuk melanjutkan eksekusi agen"""
    payload = {
        "user_id": user_id,
        "session_id": session_id,
        "response_type": response_type,
        "args": args
    }
    
    console.print("[info]Melanjutkan eksekusi agen, mohon tunggu...[/info]")
    
    with Progress() as progress:
        task = progress.add_task("[cyan]Melanjutkan eksekusi...", total=None)
        response = requests.post(f"{API_BASE_URL}/agent/resume", json=payload)
        progress.update(task, completed=100)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Gagal melanjutkan eksekusi agen: {response.status_code} - {response.text}")

def get_agent_status(user_id: str):
    """Mendapatkan status agen"""
    response = requests.get(f"{API_BASE_URL}/agent/status/{user_id}")
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Gagal mendapatkan status agen: {response.status_code} - {response.text}")

def get_system_info():
    """Mendapatkan informasi sistem"""
    response = requests.get(f"{API_BASE_URL}/system/info")
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Gagal mendapatkan informasi sistem: {response.status_code} - {response.text}")

def delete_agent_session(user_id: str):
    """Menghapus sesi pengguna"""
    response = requests.delete(f"{API_BASE_URL}/agent/session/{user_id}")
    
    if response.status_code == 200:
        return response.json()
    elif response.status_code == 404:
        # Sesi tidak ada juga dianggap berhasil
        return {"status": "success", "message": f"Sesi untuk pengguna {user_id} tidak ditemukan"}
    else:
        raise Exception(f"Gagal menghapus sesi: {response.status_code} - {response.text}")

def display_session_info(status_response):
    """
    Menampilkan informasi detail sesi, termasuk status sesi, query terakhir, data respons dll
    
    Parameter:
        status_response: Data respons status sesi
    """
    # Panel informasi sesi dasar
    user_id = status_response["user_id"]
    session_id = status_response.get("session_id", "Tidak diketahui")
    status = status_response["status"]
    last_query = status_response.get("last_query", "Tidak ada")
    last_updated = status_response.get("last_updated")
    
    # Membangun konten panel informasi
    panel_content = [
        f"ID Pengguna: {user_id}",
        f"ID Sesi: {session_id}",
        f"Status: [bold]{status}[/bold]",
        f"Query Terakhir: {last_query}"
    ]
    
    # Menambahkan timestamp
    if last_updated:
        time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(last_updated))
        panel_content.append(f"Terakhir Diperbarui: {time_str}")
    
    # Mengatur gaya panel berdasarkan status
    if status == "interrupted":
        border_style = "yellow"
        title = "[warning]Sesi Terinterupsi[/warning]"
    elif status == "completed":
        border_style = "green"
        title = "[success]Sesi Selesai[/success]"
    elif status == "error":
        border_style = "red"
        title = "[error]Sesi Error[/error]"
    elif status == "running":
        border_style = "blue"
        title = "[info]Sesi Berjalan[/info]"
    elif status == "idle":
        border_style = "cyan"
        title = "[info]Sesi Menganggur[/info]"
    else:
        border_style = "white"
        title = "[info]Sesi Status Tidak Diketahui[/info]"
    
    # Menampilkan panel dasar
    console.print(Panel(
        "\n".join(panel_content),
        title=title,
        border_style=border_style
    ))
    
    # Menampilkan data respons tambahan
    if status_response.get("last_response"):
        last_response = status_response["last_response"]
        
        # Menampilkan data respons berbeda berdasarkan status sesi
        if status == "completed" and last_response.get("result"):
            result = last_response["result"]
            if "messages" in result:
                # Mencoba menemukan pesan asisten terakhir
                assistant_messages = [msg for msg in result["messages"] if msg.get("role") == "assistant"]
                if assistant_messages:
                    final_message = assistant_messages[-1]
                else:
                    final_message = result["messages"][-1]
                
                console.print(Panel(
                    Markdown(final_message["content"]),
                    title="[success]Jawaban Terakhir Agen[/success]",
                    border_style="green"
                ))
                
        elif status == "interrupted" and last_response.get("interrupt_data"):
            interrupt_data = last_response["interrupt_data"]
            interrupt_type = interrupt_data.get("interrupt_type", "tidak diketahui")
            tool = interrupt_data.get("tool", "Alat tidak diketahui")
            message = interrupt_data.get("message", "Membutuhkan input pengguna")
            
            console.print(f"[info]Tipe Interupsi: {interrupt_type}[/info]")
            console.print(f"[info]Alat Terkait: {tool}[/info]")
            console.print(Panel(
                message,
                title=f"[warning]Pesan Interupsi - {tool}[/warning]",
                border_style="yellow"
            ))
            
        elif status == "error":
            error_msg = last_response.get("message", "Error tidak diketahui")
            console.print(Panel(
                error_msg,
                title="[error]Pesan Error[/error]",
                border_style="red"
            ))

def check_and_restore_session(user_id: str):
    """
    Memeriksa status sesi pengguna dan mencoba memulihkannya
    
    Parameter:
        user_id: ID pengguna
        
    Mengembalikan:
        tuple: (apakah ada sesi aktif, respons status sesi)
    """
    try:
        # Mendapatkan status sesi pengguna
        status_response = get_agent_status(user_id)
        
        # Jika sesi tidak ditemukan
        if status_response["status"] == "not_found":
            console.print("[info]Tidak ditemukan sesi yang ada, akan membuat sesi baru[/info]")
            return False, None
            
        # Menampilkan detail sesi
        console.print(Panel(
            f"ID Pengguna: {user_id}\n"
            f"ID Sesi: {status_response.get('session_id', 'Tidak diketahui')}\n"
            f"Status: [bold]{status_response['status']}[/bold]\n"
            f"Query Terakhir: {status_response.get('last_query', 'Tidak ada')}\n"
            f"Terakhir Diperbarui: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(status_response['last_updated'])) if status_response.get('last_updated') else 'Tidak diketahui'}\n",
            title="[info]Ditemukan Sesi yang Ada[/info]",
            border_style="cyan"
        ))
        
        # Menampilkan informasi detail sesi
        display_session_info(status_response)
        
        # Menangani secara otomatis berdasarkan status sesi
        if status_response["status"] == "interrupted":
            console.print(Panel(
                "Sesi dalam keadaan terinterupsi, membutuhkan respons Anda untuk melanjutkan.\n"
                "Sistem akan secara otomatis memulihkan titik interupsi terakhir, Anda perlu memberikan keputusan.",
                title="[warning]Sesi Terinterupsi[/warning]",
                border_style="yellow"
            ))
            
            # Jika ada respons terakhir dan memiliki data interupsi
            if (status_response.get("last_response") and 
                status_response["last_response"].get("interrupt_data")):
                
                # Menampilkan tipe interupsi dan informasi terkait
                interrupt_data = status_response["last_response"]["interrupt_data"]
                interrupt_type = interrupt_data.get("interrupt_type", "Tipe tidak diketahui")
                tool = interrupt_data.get("tool", "Alat tidak diketahui")
                
                console.print(f"[info]Tipe Interupsi: {interrupt_type}[/info]")
                console.print(f"[info]Alat Terkait: {tool}[/info]")
                
                # Memulihkan penanganan interupsi secara otomatis
                console.print("[info]Memulihkan penanganan interupsi secara otomatis...[/info]")
                return True, status_response
            else:
                console.print("[warning]Sesi terinterupsi tidak memiliki data interupsi yang diperlukan, tidak dapat dipulihkan[/warning]")
                console.print("[info]Akan membuat sesi baru[/info]")
                return False, None
                
        elif status_response["status"] == "completed":
            console.print(Panel(
                "Sesi telah selesai, hasil respons terakhir tersedia.\n"
                "Sistem akan menampilkan hasil terakhir dan secara otomatis memulai sesi baru.",
                title="[success]Sesi Selesai[/success]",
                border_style="green"
            ))
            
            # Menampilkan hasil terakhir
            if (status_response.get("last_response") and 
                status_response["last_response"].get("result")):
                
                # Mengekstrak dan menampilkan hasil
                last_result = status_response["last_response"]["result"]
                if "messages" in last_result:
                    # Mencoba menemukan pesan asisten terakhir
                    assistant_messages = [msg for msg in last_result["messages"] if msg.get("role") == "assistant"]
                    if assistant_messages:
                        final_message = assistant_messages[-1]
                    else:
                        final_message = last_result["messages"][-1]
                        
                    console.print(Panel(
                        Markdown(final_message["content"]),
                        title="[success]Jawaban Terakhir Agen[/success]",
                        border_style="green"
                    ))
            
            console.print("[info]Memulai sesi baru secara otomatis...[/info]")
            return False, None
                
        elif status_response["status"] == "error":
            # Mendapatkan pesan error
            error_msg = "Error tidak diketahui"
            if status_response.get("last_response"):
                error_msg = status_response["last_response"].get("message", "Error tidak diketahui")
                
            console.print(Panel(
                f"Sesi terakhir mengalami error: {error_msg}\n"
                "Sistem akan secara otomatis memulai sesi baru.",
                title="[error]Error Sesi[/error]",
                border_style="red"
            ))
            
            console.print("[info]Memulai sesi baru secara otomatis...[/info]")
            return False, None
                
        elif status_response["status"] == "running":
            console.print(Panel(
                "Sesi sedang berjalan, ini mungkin karena:\n"
                "1. Klien lain sedang menggunakan sesi ini\n"
                "2. Sesi sebelumnya berakhir tidak normal, status tidak diperbarui\n"
                "Sistem akan secara otomatis menunggu perubahan status sesi.",
                title="[warning]Sesi Sedang Berjalan[/warning]",
                border_style="yellow"
            ))
            
            # Menunggu perubahan status sesi secara otomatis
            console.print("[info]Menunggu perubahan status sesi secara otomatis...[/info]")
            with Progress() as progress:
                task = progress.add_task("[cyan]Menunggu sesi selesai...", total=None)
                max_attempts = 30  # Maksimal menunggu 30 detik
                attempt_count = 0
                
                for i in range(max_attempts):
                    attempt_count = i
                    # Memeriksa status
                    current_status = get_agent_status(user_id)
                    if current_status["status"] != "running":
                        progress.update(task, completed=100)
                        console.print(f"[success]Status sesi telah diperbarui menjadi: {current_status['status']}[/success]")
                        break
                    time.sleep(1)
                
                # Jika waktu tunggu habis
                if attempt_count >= max_attempts - 1:
                    console.print("[warning]Waktu tunggu habis, sesi mungkin masih berjalan[/warning]")
                    console.print("[info]Untuk menghindari konflik, akan membuat sesi baru[/info]")
                    return False, None
                
                # Mendapatkan status terbaru (panggilan rekursif)
                return check_and_restore_session(user_id)
        
        elif status_response["status"] == "idle":
            console.print(Panel(
                "Sesi dalam keadaan siaga, siap menerima query baru.\n"
                "Sistem akan secara otomatis menggunakan sesi yang ada.",
                title="[info]Sesi Siaga[/info]",
                border_style="blue"
            ))
            
            # Menggunakan sesi yang ada secara otomatis
            console.print("[info]Menggunakan sesi yang ada secara otomatis[/info]")
            return True, status_response
        
        else:
            # Status tidak dikenal
            console.print(Panel(
                f"Sesi dalam status tidak dikenal: {status_response['status']}\n"
                "Sistem akan secara otomatis membuat sesi baru untuk menghindari masalah potensial.",
                title="[warning]Status Tidak Dikenal[/warning]",
                border_style="yellow"
            ))
            
            console.print("[info]Membuat sesi baru secara otomatis...[/info]")
            return False, None
            
    except Exception as e:
        console.print(f"[error]Error saat memeriksa status sesi: {str(e)}[/error]")
        console.print(traceback.format_exc())
        console.print("[info]Akan membuat sesi baru[/info]")
        return False, None

def handle_tool_use_approval(interrupt_data, user_id, session_id):
    """
    Menangani interupsi tipe persetujuan penggunaan alat
    
    Parameter:
        interrupt_data: Data interupsi
        user_id: ID pengguna
        session_id: ID sesi
        
    Mengembalikan:
        Respons setelah penanganan
    """
    tool_name = interrupt_data.get("tool", "Alat tidak diketahui")
    message = interrupt_data.get("message", "Membutuhkan input Anda")
    
    # Menampilkan prompt persetujuan penggunaan alat
    console.print(Panel(
        f"{message}",
        title=f"[warning]Persetujuan Penggunaan Alat - {tool_name}[/warning]",
        border_style="yellow"
    ))
    
    # Mendapatkan opsi respons yang diharapkan
    expected_responses = interrupt_data.get("expected_responses", ["accept", "reject", "edit"])
    
    # Memetakan ke opsi yang ramah pengguna
    options = []
    if "accept" in expected_responses:
        options.append("yes")
    if "reject" in expected_responses:
        options.append("no")
    if "edit" in expected_responses:
        options.append("edit")
    
    # Loop input pengguna sampai mendapat input yang valid
    while True:
        # Membangun prompt pilihan
        choice_prompt = "[highlight]Pilihan Anda[/highlight]"
        if options:
            choice_prompt += f" ({'/'.join(options)})"
        
        user_input = Prompt.ask(choice_prompt)
        
        # Pemetaan tipe respons
        response_map = {
            "yes": "accept", 
            "no": "reject", 
            "edit": "edit"
        }
        
        if user_input.lower() in response_map:
            response_type = response_map[user_input.lower()]
            
            # Menangani kasus edit
            args = None
            if response_type == "edit":
                args = handle_interrupt_edit(interrupt_data)
                
            # Melanjutkan eksekusi
            console.print(f"[info]Melanjutkan eksekusi dengan respons '{response_type}'...[/info]")
            new_response = resume_agent(user_id, session_id, response_type, args=args)
            
            # Menangani interupsi baru atau hasil akhir secara rekursif
            return process_agent_response(new_response, user_id)
        else:
            console.print(f"[error]Input tidak valid, silakan masukkan opsi yang valid: {', '.join(options)}[/error]")
            # Melanjutkan loop, mendapatkan input pengguna lagi

def handle_unknown_interrupt(interrupt_data, user_id, session_id):
    """
    Menangani interupsi dengan tipe tidak dikenal
    
    Parameter:
        interrupt_data: Data interupsi
        user_id: ID pengguna
        session_id: ID sesi
        
    Mengembalikan:
        Respons setelah penanganan
    """
    tool_name = interrupt_data.get("tool", "Alat tidak diketahui")
    message = interrupt_data.get("message", "Membutuhkan input Anda")
    interrupt_type = interrupt_data.get("interrupt_type", "tidak diketahui")
    
    # Menampilkan prompt interupsi umum
    console.print(Panel(
        f"{message}",
        title=f"[warning]Agen Membutuhkan Keputusan Anda - {interrupt_type}[/warning]",
        border_style="yellow"
    ))
    
    # Mendapatkan opsi respons yang diharapkan
    expected_responses = interrupt_data.get("expected_responses", ["accept", "reject"])
    
    # Memetakan ke opsi yang ramah pengguna
    options = []
    if "accept" in expected_responses:
        options.append("yes")
    if "reject" in expected_responses:
        options.append("no")
    if "edit" in expected_responses:
        options.append("edit")
    
    # Loop input pengguna sampai mendapat input yang valid
    while True:
        # Membangun prompt pilihan
        choice_prompt = "[highlight]Pilihan Anda[/highlight]"
        if options:
            choice_prompt += f" ({'/'.join(options)})"
        
        user_input = Prompt.ask(choice_prompt)
        
        # Pemetaan tipe respons
        response_map = {
            "yes": "accept", 
            "no": "reject", 
            "edit": "edit"
        }
        
        if user_input.lower() in response_map:
            response_type = response_map[user_input.lower()]
            
            # Menangani kasus edit
            args = None
            if response_type == "edit":
                args = handle_interrupt_edit(interrupt_data)
                
            # Melanjutkan eksekusi
            console.print(f"[info]Melanjutkan eksekusi dengan respons '{response_type}'...[/info]")
            new_response = resume_agent(user_id, session_id, response_type, args=args)
            
            # Menangani interupsi baru atau hasil akhir secara rekursif
            return process_agent_response(new_response, user_id)
        else:
            console.print(f"[error]Input tidak valid, silakan masukkan opsi yang valid: {', '.join(options)}[/error]")
            # Melanjutkan loop, mendapatkan input pengguna lagi

def handle_interrupt_edit(interrupt_data):
    """
    Menangani operasi edit interupsi
    
    Parameter:
        interrupt_data: Data interupsi
        
    Mengembalikan:
        Parameter yang telah diedit
    """
    # Mendapatkan kunci parameter yang perlu diedit
    edit_keys = interrupt_data.get("args", {}).keys()
    
    if "query" in edit_keys:
        new_query = Prompt.ask("[highlight]Silakan masukkan konten pencarian baru[/highlight]")
        return {"query": new_query}
    else:
        # Penanganan edit umum
        console.print("[info]Silakan berikan nilai parameter baru[/info]")
        args = {}
        for key in edit_keys:
            value = Prompt.ask(
                f"[highlight]{key}[/highlight]", 
                default=str(interrupt_data["args"][key])
            )
            args[key] = value
        return args

def handle_completed_response(response):
    """
    Menangani respons yang telah selesai
    
    Parameter:
        response: Data respons
        
    Mengembalikan:
        Hasil pemrosesan
    """
    result = response.get("result", {})
    
    if not result:
        console.print("[warning]Agen mengembalikan hasil kosong[/warning]")
        return None
        
    if "messages" in result:
        # Mencari pesan asisten terakhir
        assistant_messages = [msg for msg in result["messages"] if msg.get("role") == "assistant"]
        if assistant_messages:
            final_message = assistant_messages[-1]
            console.print(Panel(
                Markdown(final_message["content"]),
                title="[success]Jawaban Agen[/success]",
                border_style="green"
            ))
        else:
            # Jika tidak ada pesan asisten, gunakan pesan terakhir
            final_message = result["messages"][-1]
            console.print(Panel(
                Markdown(final_message["content"]),
                title="[success]Pesan Terakhir[/success]",
                border_style="green"
            ))
    else:
        console.print("[warning]Agen tidak mengembalikan pesan yang valid[/warning]")
        if isinstance(result, dict):
            console.print("[info]Struktur data hasil asli:[/info]")
            console.print(result)
    
    return result

def process_agent_response(response, user_id):
    """Memproses respons agen, termasuk menangani interupsi dan menampilkan hasil"""
    # Pemeriksaan defensif, memastikan response tidak kosong
    if not response:
        console.print("[error]Menerima respons kosong, tidak dapat diproses[/error]")
        return None
    
    try:
        session_id = response["session_id"]
        status = response["status"]
        timestamp = response.get("timestamp", time.time())
        
        # Menampilkan timestamp dan ID sesi (untuk debug dan pelacakan)
        time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))
        console.print(f"[info]Waktu respons: {time_str} | ID Sesi: {session_id}[/info]")
        
        # Menangani status berbeda
        if status == "interrupted":
            # Mendapatkan data interupsi
            interrupt_data = response.get("interrupt_data", {})
            interrupt_type = interrupt_data.get("interrupt_type", "unknown")
            
            try:
                # Mendistribusikan ke fungsi penanganan berbeda berdasarkan tipe interupsi
                if interrupt_type == "tool_use_approval":
                    return handle_tool_use_approval(interrupt_data, user_id, session_id)
                else:
                    # Tipe interupsi lain akan ditangani di masa depan!
                    return handle_unknown_interrupt(interrupt_data, user_id, session_id)
            except Exception as e:
                console.print(f"[error]Kesalahan saat menangani respons interupsi: {str(e)}[/error]")
                console.print(f"[info]Status interupsi telah disimpan, Anda dapat melanjutkan sesi nanti[/info]")
                console.print(traceback.format_exc())
                return None
        
        elif status == "completed":
            # Menggunakan fungsi terpisah untuk menangani status selesai
            return handle_completed_response(response)
        
        elif status == "error":
            # Menampilkan pesan kesalahan
            error_msg = response.get("message", "Kesalahan tidak diketahui")
            console.print(Panel(
                f"{error_msg}",
                title="[error]Terjadi kesalahan dalam pemrosesan[/error]",
                border_style="red"
            ))
            return None
        
        elif status == "running":
            # Menangani status sedang berjalan
            console.print("[info]Agen sedang memproses permintaan Anda, harap tunggu...[/info]")
            return response
        
        elif status == "idle":
            # Menangani status idle
            console.print("[info]Agen dalam status idle, siap menerima permintaan baru[/info]")
            return response
            
        else:
            # Status tidak dikenal lainnya
            console.print(f"[warning]Agen dalam status tidak dikenal: {status} - {response.get('message', 'Tidak ada pesan')}[/warning]")
            return response
            
    except KeyError as e:
        console.print(f"[error]Format respons salah, field kunci {e} tidak ditemukan[/error]")
        return None
    except Exception as e:
        console.print(f"[error]Terjadi kesalahan tidak terduga saat memproses respons agen: {str(e)}[/error]")
        console.print(traceback.format_exc())
        return None

def main():
    """Fungsi utama, menjalankan klien"""
    console.print(Panel(
        "Klien Frontend Agen (Versi Pemulihan Sesi Otomatis)",
        title="[heading]Sistem Interaksi Agen[/heading]",
        border_style="magenta"
    ))
    
    # Mencoba mendapatkan informasi sistem
    try:
        system_info = get_system_info()
        console.print(f"[info]Sesi sistem aktif: {system_info['sessions_count']}[/info]")
        if system_info['active_users']:
            console.print(f"[info]Pengguna aktif: {', '.join(system_info['active_users'])}[/info]")
    except Exception:
        console.print("[warning]Tidak dapat mengambil status sistem, tapi ini tidak mempengaruhi penggunaan[/warning]")
    
    # Mendapatkan ID pengguna (dalam aplikasi nyata mungkin dari login)
    default_user_id = f"user_{int(time.time())}"
    user_id = Prompt.ask("[info]Masukkan ID pengguna[/info] (ID baru membuat sesi baru, ID yang ada akan dipulihkan otomatis)", default=default_user_id)
    
    # Memeriksa dan mencoba memulihkan sesi yang ada
    has_active_session, session_status = check_and_restore_session(user_id)
    
    # Loop interaksi utama
    while True:
        try:
            # Penanganan pemulihan sesi - berdasarkan status
            if has_active_session and session_status:

                # Jika status interupsi, tangani interupsi secara otomatis
                if session_status["status"] == "interrupted":
                    console.print("[info]Menangani sesi yang terinterupsi...[/info]")
                    if "last_response" in session_status and session_status["last_response"]:
                        # Menggunakan process_agent_response untuk menangani interupsi sebelumnya
                        result = process_agent_response(session_status["last_response"], user_id)
                        
                        # Memeriksa status kembali
                        current_status = get_agent_status(user_id)
                        
                        # Jika sesi selesai setelah menangani interupsi, buat sesi baru otomatis
                        if current_status["status"] == "completed":
                            # Menampilkan pesan selesai
                            console.print("[success]Sesi telah selesai[/success]")
                            console.print("[info]Memulai sesi baru secara otomatis...[/info]")
                            has_active_session = False
                            session_status = None
                        else:
                            # Memperbarui status sesi
                            has_active_session = True
                            session_status = current_status
            
            # Mendapatkan query pengguna
            query = Prompt.ask("\n[info]Masukkan pertanyaan Anda[/info] (ketik 'exit' untuk keluar, 'status' untuk cek status, 'new' untuk sesi baru)", 
                               default="halo")
            
            # Menangani perintah khusus
            if query.lower() == 'exit':
                console.print("[info]Terima kasih telah menggunakan, sampai jumpa![/info]")
                break
            elif query.lower() == 'status':
                # Memeriksa status sesi saat ini
                status_response = get_agent_status(user_id)
                console.print(Panel(
                    f"ID Pengguna: {status_response['user_id']}\n"
                    f"ID Sesi: {status_response.get('session_id', 'Tidak diketahui')}\n"
                    f"Status Sesi: {status_response['status']}\n"
                    f"Query Terakhir: {status_response['last_query'] or 'Tidak ada'}\n"
                    f"Pembaruan Terakhir: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(status_response['last_updated'])) if status_response.get('last_updated') else 'Tidak diketahui'}\n",
                    title="[info]Status Sesi Saat Ini[/info]",
                    border_style="cyan"
                ))
                continue
            elif query.lower() == 'new':
                # Memulai sesi baru secara aktif - hapus sesi lama di server terlebih dahulu
                console.print("[info]Memulai sesi baru...[/info]")
                try:
                    delete_response = delete_agent_session(user_id)
                    console.print(f"[success]Sesi lama telah dibersihkan: {delete_response['message']}[/success]")
                except Exception as e:
                    console.print(f"[warning]Kesalahan saat membersihkan sesi lama: {str(e)}[/warning]")
                    console.print("[info]Akan melanjutkan membuat sesi baru...[/info]")
                
                # Mengatur ulang status lokal
                has_active_session = False
                session_status = None
                console.print("[success]Sesi baru siap![/success]")
                continue
                
            # Memanggil agen
            console.print("[info]Mengirim query ke agen...[/info]")
            response = invoke_agent(user_id, query)
            
            # Memproses respons agen
            result = process_agent_response(response, user_id)
            
            # Mendapatkan status terbaru
            latest_status = get_agent_status(user_id)
            
            # Menangani otomatis berdasarkan status respons
            if latest_status["status"] == "completed":
                # Menangani status selesai - otomatis mulai sesi baru
                console.print("[info]Sesi selesai, siap menerima query baru[/info]")
                has_active_session = False
                session_status = None
            elif latest_status["status"] == "error":
                # Menangani status error - otomatis mulai sesi baru
                console.print("[info]Sesi mengalami error, akan memulai sesi baru[/info]")
                has_active_session = False
                session_status = None
            else:
                # Status lain (idle, interrupted) - pertahankan sesi aktif
                has_active_session = True
                session_status = latest_status
            
        except KeyboardInterrupt:
            console.print("\n[warning]Interupsi pengguna, sedang keluar...[/warning]")
            # Menyimpan status saat ini untuk pemulihan di sesi berikutnya
            console.print("[info]Status sesi telah disimpan, dapat dipulihkan dengan ID pengguna yang sama di sesi berikutnya[/info]")
            break
        except Exception as e:
            console.print(f"[error]Terjadi kesalahan saat menjalankan: {str(e)}[/error]")
            console.print(traceback.format_exc())
            # Mencoba memulihkan atau membuat sesi baru
            has_active_session, session_status = check_and_restore_session(user_id)
            continue

if __name__ == "__main__":
    main()
