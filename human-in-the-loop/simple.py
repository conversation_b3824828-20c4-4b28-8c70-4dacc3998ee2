from typing import TypedDict
import uuid

from langgraph.checkpoint.memory import InMemorySaver
from langgraph.constants import START
from langgraph.graph import StateGraph
from langgraph.types import interrupt, Command

class State(TypedDict):
    some_text: str
    revision_count: int

def human_node(state: State):
    """Node peninjauan manusia - menghentikan eksekusi untuk menunggu input manusia"""
    print("\n*********Memasuki node peninjauan manusia*********")
    print(f"\nTeks saat ini: '{state['some_text']}'")
    print(f"Jumlah revisi: {state.get('revision_count', 0)}")
    
    # Interupsi dan tunggu input manusia
    value = interrupt({
        "text_to_revise": state["some_text"],
        "message": "Silakan berikan teks yang telah direvisi, atau ketik 'ok' untuk menerima teks saat ini"
    })
    
    # Per<PERSON>ui hitungan revisi
    revision_count = state.get('revision_count', 0)
    if value != state['some_text'] and value != 'ok':
        revision_count += 1
    
    return {
        "some_text": value if value != 'ok' else state['some_text'],
        "revision_count": revision_count
    }

# Bangun graf
graph_builder = StateGraph(State)
graph_builder.add_node("human_node", human_node)
graph_builder.add_edge(START, "human_node")
checkpointer = InMemorySaver()
graph = graph_builder.compile(checkpointer=checkpointer)

# Fungsi demo
def demo_hitl():
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    # Teks awal
    initial_state = {"some_text": "Teks asli", "revision_count": 0}
    
    print("=== Demonstrasi Human-in-the-Loop ===")
    
    # Eksekusi pertama - akan berhenti pada interupsi
    result = graph.invoke(initial_state, config)
    
    interrupt_info = result['__interrupt__'][0].value
    
    # Simulasi input manusia
    print(f"\nTeks saat ini: {interrupt_info['text_to_revise']}")
    user_input = input(f"{interrupt_info['message']}: ")
    
    # Lanjutkan eksekusi
    final_result = graph.invoke(Command(resume=user_input), config)
    
    print(f"\nHasil akhir:")
    print(f"Teks: '{final_result['some_text']}'")
    print(f"Jumlah revisi: {final_result['revision_count']}")

if __name__ == "__main__":
    demo_hitl()