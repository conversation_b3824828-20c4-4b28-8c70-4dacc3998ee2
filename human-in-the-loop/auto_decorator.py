"""
Implementasi pola dekorator untuk pemanggilan alat dengan tinjauan manusia

Versi ini memperbaiki berbagai bug dan menyederhanakan implementasi
"""
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.types import interrupt
from langgraph.prebuilt import create_react_agent
import json
import asyncio
from typing import Optional, Tuple, Dict, Any, Callable, Union, cast
from functools import wraps
from langchain_openai import ChatOpenAI
from langgraph.types import Command
from langchain_tavily import TavilySearch
from langchain_core.tools import BaseTool, tool
from langchain_core.runnables import RunnableConfig
import os
import inspect

def human_in_the_loop():
    """
    Dekorator untuk menambahkan fungsi tinjauan manusia ke fungsi alat
    
    Return:
        Fungsi dekorator
    """
    def decorator(func: Callable):
        # Dapatkan informasi signature dari fungsi asli
        tool_name = func.__name__
        
        @tool(tool_name)  # Gunakan nama alat sebagai parameter pertama
        @wraps(func)  # Pertahankan metadata fungsi asli
        def wrapper(**kwargs):
            # Buat permintaan interupsi
            interrupt_data = {
                "tool": tool_name,
                "args": kwargs,
                "message": f"Bersiap menggunakan alat {tool_name}: parameter={kwargs}\nIzinkan melanjutkan?\nMasukkan 'yes' untuk menerima, 'no' untuk menolak, atau 'edit' untuk mengubah parameter"
            }
            
            # Mulai interupsi, tunggu tinjauan manusia
            response = interrupt(interrupt_data)
            print(f"Respons tinjauan: {response}")
            
            # Tangani respons manusia
            if response["type"] == "accept":
                print(f"Pemanggilan alat disetujui, menjalankan {tool_name}...")
                try:
                    result = func(**kwargs)
                    return result
                except Exception as e:
                    error_msg = f"Pemanggilan alat gagal: {str(e)}"
                    print(error_msg)
                    return error_msg
            
            elif response["type"] == "edit":
                edited_args = response.get("args", {})
                print(f"Menggunakan parameter yang diubah: {edited_args}")
                try:
                    result = func(**edited_args)
                    return result
                except Exception as e:
                    error_msg = f"Pemanggilan alat dengan parameter yang diubah gagal: {str(e)}"
                    print(error_msg)
                    return error_msg
            
            elif response["type"] == "reject":
                print("Pemanggilan alat ditolak")
                return "Alat ini ditolak, silakan coba metode lain atau berikan jawaban tanpa menggunakan alat."
            
            else:
                error_msg = f"Tipe respons tidak didukung: {response['type']}"
                print(error_msg)
                return error_msg
                
        return wrapper
    
    return decorator

# Buat LLM
model = ChatOpenAI(model='gpt-4o-mini')

# Gunakan dekorator untuk menambahkan tinjauan manusia ke alat pencarian Tavily
@human_in_the_loop()
def tavily_search(query: str, search_depth: str = "basic"):
    """Lakukan pencarian web menggunakan Tavily"""
    try:
        search = TavilySearch(api_key=os.environ.get("TAVILY_API_KEY"))
        search_results = search.invoke({
            "query": query, 
            "search_depth": search_depth,
            "max_results": 5
        })
        return json.dumps(search_results, ensure_ascii=False, indent=2)
    except Exception as e:
        return f"Pemanggilan API Tavily gagal: {str(e)}"

def create_tavily_search_agent():
    """Buat agen dengan alat pencarian Tavily"""
    # Buat penyimpan checkpoint
    checkpointer = InMemorySaver() 
    
    # Konfigurasi agen
    config = {
        "configurable": {
            "thread_id": "1"
        }
    }
    
    # Buat agen
    agent = create_react_agent(
        model=model,
        tools=[tavily_search],
        checkpointer=checkpointer
    )
    
    return agent, config

async def main():
    """Fungsi utama, membuat dan menjalankan agen secara asinkron"""
    # Buat agen
    agent, config = create_tavily_search_agent()
    
    # Mulai proses
    result = await agent.ainvoke(
        {"messages": [{"role": "system", "content": "Anda akan menggunakan alat untuk membantu pengguna. Jika penggunaan alat ditolak, beri tahu pengguna."},
                      {"role": "user", "content": "Berita terbaru perang dagang AS-China"}]},
        config=config
    )
    
    # Tangani kemungkinan interupsi
    while "__interrupt__" in result:
        interrupt_info = result["__interrupt__"][0].value
        print("\n" + "="*50)
        print(f"Menerima permintaan interupsi")
        print(f"{interrupt_info.get('message', '')}")
        print("="*50)
        user_input = input("Silakan masukkan: ")
        
        # Tangani input pengguna
        while True:
            if user_input.lower() == "yes":
                result = await agent.ainvoke(
                    Command(resume={"type": "accept"}),
                    config=config
                )
                break
            elif user_input.lower() == "no":
                result = await agent.ainvoke(
                    Command(resume={"type": "reject"}),
                    config=config
                )
                break
            elif user_input.lower() == "edit":
                print("Masukkan konten pencarian baru: ", end="")
                new_query = input()
                result = await agent.ainvoke(
                    Command(resume={"type": "edit", "args": {"query": new_query}}),
                    config=config
                )
                break
            else:
                print("Input tidak valid, masukkan 'yes', 'no' atau 'edit': ", end="")
                user_input = input()
    
    # Cetak hasil akhir
    if result.get("messages"):
        print("\n=== Jawaban Akhir ===")
        print(result['messages'][-1].content)
    
    return result

if __name__ == "__main__":
    asyncio.run(main())
